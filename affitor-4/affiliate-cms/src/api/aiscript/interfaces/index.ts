export interface IAIScript {
  id: string;
  title: string;
  content: string;
  provider: 'gpt' | 'claude' | 'gemini';
  model_version: string;
  parameters?: Record<string, any>;
  aiscript_session?: IAIScriptSession;
  prompt?: IPrompt;
}

export interface IAIScriptSession {
  id: string;
  session_id: string;
  start_time: Date;
  end_time?: Date;
  session_status: 'active' | 'ended';
  aiscripts?: IAIScript[];
  input?: string;
  output?: string;
  users_permissions_user?: any;
  prompt_id?: string;
}

export interface IPrompt {
  id: string;
  title: string;
  content: string;
  description?: string;
}
