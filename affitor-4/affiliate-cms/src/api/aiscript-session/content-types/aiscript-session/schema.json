{"kind": "collectionType", "collectionName": "aiscript_sessions", "info": {"singularName": "aiscript-session", "pluralName": "aiscript-sessions", "displayName": "AI Script Session", "description": "Stores AI script session data"}, "options": {"draftAndPublish": false}, "attributes": {"session_id": {"type": "string", "required": true, "unique": true}, "start_time": {"type": "datetime", "required": true}, "end_time": {"type": "datetime"}, "session_status": {"type": "enumeration", "enum": ["active", "ended"], "default": "active", "required": true}, "aiscripts": {"type": "relation", "relation": "oneToMany", "target": "api::aiscript.aiscript", "mappedBy": "aiscript_session"}, "users_permissions_user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}}}