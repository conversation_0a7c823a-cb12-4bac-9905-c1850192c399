'use strict';
import {
  TiktokClient,
  YoutubeClient,
  TiktokTranscriptClient,
  YoutubeTranscriptClient,
  TiktokDownloadClient,
} from '../../../utils/request';
import { IRedditPost, IVideo, IXPost } from '../interface';
import crypto from 'crypto';
import { ruleMap } from '../../../utils/cron';
import { downloadImage, cleanupFile } from '../../../utils/file-download';
import { s3UploadService } from '../../../utils/s3-upload';

import { factories } from '@strapi/strapi';
import _ from 'lodash';
import TikTokCrawler from './tiktok-crawler';
import YouTubeCrawler from './youtube-crawler';

// Define interface for TikTok video detail response
interface ITikTokVideoDetailResponse {
  success?: boolean;
  thumbnail?: string;
  [key: string]: any;
}

// Remove or comment out this constant as we'll use global config instead
// const EMPTY_RESULT_REFRESH_INTERVAL = 5 * 60 * 1000;

export default factories.createCoreService('api::social-listening.social-listening', {
  async processAffiliateSocialData(
    affiliateDocId: string,
    platforms: string[] = ['youtube', 'tiktok', 'x', 'reddit']
  ) {
    try {
      const affiliate = await getAffiliate(affiliateDocId);
      let keywordsYoutube = [];
      if (affiliate?.brand_keywords_youtube) {
        keywordsYoutube = affiliate.brand_keywords_youtube.split('|');
      }

      let keywordsTiktok = [];
      if (affiliate?.brand_keywords_tiktok) {
        keywordsTiktok = affiliate.brand_keywords_tiktok.split('|');
      }

      if (!keywordsYoutube.length && !keywordsTiktok.length) return;

      // Create an array of all promises
      const youtubePromises = keywordsYoutube.map((keyword) =>
        searchPosts(keyword, affiliate, ['youtube'])
      );

      const tiktokPromises = keywordsTiktok.map((keyword) =>
        searchPosts(keyword, affiliate, ['tiktok'])
      );

      // Run all promises in parallel
      await Promise.all([...tiktokPromises, ...youtubePromises]);
    } catch (err) {
      console.error('LOG-err', err);
      return;
    }
  },

  async getTranscript(videoId: string, userId: number) {
    try {
      // Check if we already have a transcript for this video
      const socialListening = await strapi.db
        .query('api::social-listening.social-listening')
        .findOne({
          where: {
            $or: [{ video_id: videoId }],
          },
        });

      if (!socialListening) {
        throw new Error('Video not found');
      }

      const prompts = await strapi.entityService.findMany('api::prompt.prompt', {
        filters: { type: 'aiscript' },
      });

      // Initialize transcript
      let transcript = socialListening.transcript || '';

      // Fetch transcript if we don't have it already
      if (!transcript) {
        transcript = await this.fetchTranscriptByPlatform(socialListening);

        // Update the social listening record with the transcript
        await strapi.db.query('api::social-listening.social-listening').update({
          where: { id: socialListening.id },
          data: { transcript },
        });
      }

      // // Create a new session for the AI script processing
      // const session = await strapi.entityService.create('api::aiscript-session.aiscript-session', {
      //   data: {
      //     session_id: `transcript-${socialListening.video_id}-${new Date().toISOString()}`,
      //     session_status: 'active',
      //     start_time: new Date(),
      //     users_permissions_user: userId,
      //   },
      // });

      // // Process with AI service in all cases
      // await strapi.service('api::aiscript.aiscript').processUserScript({
      //   message: transcript,
      //   session,
      //   isUseDefaultPrompt: false,
      // });

      // Return consistent response structure
      return {
        transcript,
        suggestions: prompts,
        // sessionId: session.session_id,
      };
    } catch (err) {
      console.error('Error fetching transcript:', err);
      throw err;
    }
  },

  // Helper function to fetch transcript based on platform
  async fetchTranscriptByPlatform(socialListening) {
    switch (socialListening.platform) {
      case 'youtube': {
        const youtubeResult = await YoutubeTranscriptClient.getTranscript(socialListening.video_id);
        return youtubeResult.transcript || '';
      }

      case 'tiktok': {
        if (!socialListening.video_link) {
          throw new Error('Video link not available for TikTok video');
        }
        const tiktokResult = await TiktokTranscriptClient.getTranscript(socialListening.video_link);
        return tiktokResult.transcript || '';
      }

      default:
        throw new Error('Transcript not available for this platform');
    }
  },

  async getTiktokDownloadLink(videoId: string) {
    try {
      // Check if we have the video in our database
      const socialListening = await strapi.db
        .query('api::social-listening.social-listening')
        .findOne({
          where: {
            video_id: videoId,
            platform: 'tiktok',
          },
        });

      if (!socialListening) {
        throw new Error('TikTok video not found');
      }

      // Get the video link from our database
      const videoLink = socialListening.video_link;
      if (!videoLink) {
        throw new Error('Video link not available for TikTok video');
      }

      // Get the download link from the TikTok Download API
      const result = await TiktokDownloadClient.getDownloadLink(videoLink);

      return {
        success: true,
        data: {
          play: result?.data?.play || '',
        },
      };
    } catch (err) {
      console.error('Error getting TikTok download link:', err);
      return {
        success: false,
        error: err.message,
      };
    }
  },

  async getTiktokVideoDetailWithCover(videoId: string) {
    try {
      console.log(`Getting TikTok video detail with cover for video ID: ${videoId}`);

      // First, get the video detail from TikTok API
      const videoDetail = await TiktokClient.getVideoDetail(videoId);

      if (!videoDetail.success || !videoDetail.thumbnail) {
        throw new Error('Failed to get video details from TikTok API');
      }

      const thumbnailUrl = videoDetail.thumbnail;
      console.log(`Found thumbnail URL: ${thumbnailUrl}`);

      // Download the cover image
      let downloadResult;
      try {
        downloadResult = await downloadImage(
          thumbnailUrl,
          `tiktok_cover_${videoId}`,
          undefined // Use default temp directory
        );
        console.log(`Downloaded cover image: ${downloadResult.fileName}`);
      } catch (downloadError) {
        console.error('Error downloading cover image:', downloadError);
        throw new Error(`Failed to download cover image: ${downloadError.message}`);
      }

      // Upload to S3
      let s3Result;
      try {
        // Generate a unique filename for the TikTok cover
        const s3FileName = s3UploadService.generateTikTokCoverFileName(
          videoId,
          downloadResult.fileExtension || '.jpg'
        );

        s3Result = await s3UploadService.uploadImage(
          downloadResult.filePath,
          s3FileName,
          'tiktok-covers' // Folder within S3 bucket
        );
        console.log(`Uploaded to S3: ${s3Result.publicUrl}`);
      } catch (uploadError) {
        console.error('Error uploading to S3:', uploadError);
        // Clean up downloaded file
        cleanupFile(downloadResult.filePath);
        throw new Error(`Failed to upload to S3: ${uploadError.message}`);
      }

      // Clean up the temporary downloaded file
      cleanupFile(downloadResult.filePath);

      // Update the database record if it exists
      try {
        const socialListening = await strapi.db
          .query('api::social-listening.social-listening')
          .findOne({
            where: {
              video_id: videoId,
              platform: 'tiktok',
            },
          });

        if (socialListening) {
          await strapi.db.query('api::social-listening.social-listening').update({
            where: { id: socialListening.id },
            data: {
              thumbnail: s3Result.publicUrl,
              updatedAt: new Date(),
            },
          });
          console.log(`Updated database record for video ${videoId}`);
        }
      } catch (dbError) {
        console.error('Error updating database:', dbError);
        // Don't throw here as the main operation succeeded
      }

      return {
        success: true,
        data: {
          videoId,
          originalThumbnail: thumbnailUrl,
          s3Url: s3Result.publicUrl,
          s3FileKey: s3Result.fileKey,
          s3Bucket: s3Result.bucket,
          fileName: s3Result.fileName,
          videoDetail: videoDetail,
        },
      };
    } catch (error) {
      console.error('Error in getTiktokVideoDetailWithCover:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  },

  async updateTiktokThumbnails() {
    try {
      // Get all TikTok videos from the database
      const videos = await strapi.db.query('api::social-listening.social-listening').findMany({
        where: {
          platform: 'tiktok',
        },
        select: ['id', 'video_id', 'thumbnail'],
      });

      if (!videos || videos.length === 0) {
        return {
          success: true,
          message: 'No TikTok videos found to update',
        };
      }

      console.log(`Found ${videos.length} TikTok videos to process`);

      // Process in batches with improved efficiency
      const batchSize = 50; // 50 requests per batch
      const requestDelay = 20; // 20ms delay between requests = ~50 req/sec
      let updatedCount = 0;
      let failedCount = 0;
      let skippedCount = 0;

      // Create a rate-limited version of getVideoDetail (max 8 req/sec to stay well under 600/min limit)
      const rateLimit = 8;
      const getVideoDetailRateLimited = this.rateLimiter(
        TiktokClient.getVideoDetail.bind(TiktokClient),
        rateLimit
      );

      console.log(`Set up rate limiting for TikTok API at ${rateLimit} requests per second`);

      // Process videos in batches
      for (let i = 0; i < videos.length; i += batchSize) {
        console.log(
          `Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(videos.length / batchSize)}`
        );
        const batch = videos.slice(i, Math.min(i + batchSize, videos.length));

        // Prepare batch promises with rate limiting
        const batchPromises = batch.map((video, index) => {
          // eslint-disable-next-line no-async-promise-executor
          return new Promise<any>(async (resolve) => {
            // Delay each request to prevent hitting rate limits
            await new Promise((r) => setTimeout(r, index * requestDelay));

            if (!video.video_id) {
              resolve({ skipped: true, id: video.id });
              return;
            }

            try {
              // Use the rate-limited method instead of direct API call
              const response = await getVideoDetailRateLimited(video.video_id);

              // Properly type the response
              const typedResponse = response as ITikTokVideoDetailResponse;

              // Make sure we're properly extracting the thumbnail from the response
              if (
                typedResponse &&
                typedResponse.success === true &&
                typedResponse.thumbnail &&
                typedResponse.thumbnail !== video.thumbnail
              ) {
                resolve({
                  id: video.id,
                  thumbnail: typedResponse.thumbnail,
                  updated: true,
                });
              } else {
                resolve({ skipped: true, id: video.id });
              }
            } catch (error) {
              console.error(`Error processing video ${video.video_id}:`, error);
              resolve({ failed: true, id: video.id });
            }
          });
        });

        // Process all videos in this batch concurrently (with built-in delay for rate limiting)
        const batchResults = await Promise.all(batchPromises);

        // Collect updates from this batch
        const updates = batchResults.filter((result) => result.updated);
        const updateMap = {};

        // Count results
        batchResults.forEach((result) => {
          if (result.updated && result.thumbnail) {
            // Verify thumbnail exists
            updatedCount++;
            updateMap[result.id] = result.thumbnail;
          } else if (result.failed) {
            failedCount++;
          } else if (result.skipped) {
            skippedCount++;
          }
        });

        // Perform bulk update if we have updates
        if (Object.keys(updateMap).length > 0) {
          try {
            // Log the update map for debugging
            console.log(`Updating thumbnails for ${Object.keys(updateMap).length} videos`);

            const knex = strapi.db.connection;
            await knex.transaction(async (trx) => {
              // Build CASE statement for thumbnails with proper validation
              const cases = [];
              const params = [];

              for (const [id, thumbnail] of Object.entries(updateMap)) {
                // Ensure both id and thumbnail are defined
                if (id && thumbnail) {
                  cases.push('WHEN ? THEN ?');
                  params.push(id, thumbnail);
                }
              }

              // Only proceed if we have valid cases to update
              if (cases.length > 0) {
                const thumbnailCase = knex.raw(
                  'CASE id ' + cases.join(' ') + ' ELSE thumbnail END',
                  params
                );

                // Update records in a single query
                await knex('social_listenings')
                  .update({
                    updated_at: new Date(),
                    thumbnail: thumbnailCase,
                  })
                  .whereIn('id', Object.keys(updateMap))
                  .transacting(trx);

                console.log(`Successfully updated ${cases.length} thumbnails in current batch`);
              } else {
                console.log('No valid thumbnails to update in this batch');
              }
            });
          } catch (error) {
            console.error('Error performing bulk update:', error);
            failedCount += Object.keys(updateMap).length;
            updatedCount -= Object.keys(updateMap).length;
          }
        }

        // Add a small pause between batches to prevent rate limiting issues
        if (i + batchSize < videos.length) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }

      return {
        success: true,
        message: `Processed ${videos.length} TikTok videos. Updated: ${updatedCount}, Failed: ${failedCount}, Skipped: ${skippedCount}.`,
        details: {
          total: videos.length,
          updated: updatedCount,
          failed: failedCount,
          skipped: skippedCount,
        },
      };
    } catch (error) {
      console.error('Error updating TikTok video thumbnails:', error);
      return {
        success: false,
        message: `Error updating TikTok video thumbnails: ${error.message}`,
      };
    }
  },

  async getTiktokThumbnailUpdateSchedule() {
    const defaultRule = '0 0 */3 * *'; // Default to every 3 days at midnight

    try {
      // Get the cron schedule from global config, or use a default if not set
      const globalConfig = await strapi.entityService.findMany('api::global.global');
      const tiktokUpdateSchedule = globalConfig?.tiktok_thumbnail_update_schedule || null;

      if (!tiktokUpdateSchedule) {
        strapi.log.info(
          'No tiktok_thumbnail_update_schedule found in global config, using default'
        );
        return defaultRule;
      }

      strapi.log.info(
        `Using TikTok thumbnail update schedule from global config: ${tiktokUpdateSchedule}`
      );
      return ruleMap[tiktokUpdateSchedule] || defaultRule;
    } catch (error) {
      strapi.log.error('Error getting TikTok thumbnail update schedule:', error);
      return defaultRule;
    }
  },

  /**
   * Rate limiter to control API request rate
   * @param {Function} fn - The function to rate limit
   * @param {number} ratePerSecond - Maximum number of requests per second
   * @returns {Function} Rate-limited function
   */
  rateLimiter(fn, ratePerSecond = 3) {
    const queue = [];
    const requestTimestamps = [];
    let processing = false;

    // Calculate delay needed to maintain rate limit
    const getDelayMs = () => {
      const now = Date.now();

      // Clean up old timestamps (older than 1 second)
      while (requestTimestamps.length > 0 && now - requestTimestamps[0] >= 1000) {
        requestTimestamps.shift();
      }

      // If we haven't hit our rate limit yet, no delay needed
      if (requestTimestamps.length < ratePerSecond) {
        return 0;
      }

      // Calculate when the next slot will be available
      // This is when the oldest request will be more than 1 second old
      const nextAvailableTime = requestTimestamps[0] + 1000;
      const delayMs = Math.max(0, nextAvailableTime - now + 10); // Add 10ms buffer

      return delayMs;
    };

    // Process the queue with proper rate limiting
    const processQueue = async () => {
      if (processing || queue.length === 0) return;

      processing = true;
      console.log(
        `Starting to process queue of ${queue.length} items with rate limit of ${ratePerSecond}/s`
      );

      try {
        while (queue.length > 0) {
          const delayMs = getDelayMs();

          if (delayMs > 0) {
            // Wait until we can process the next request
            console.log(
              `Rate limit: waiting ${delayMs}ms before next request (${requestTimestamps.length}/${ratePerSecond} slots used)`
            );
            await new Promise((resolve) => setTimeout(resolve, delayMs));
          }

          // Process next item in queue
          const { params, resolve, reject } = queue.shift();

          // Record this request timestamp
          const timestamp = Date.now();
          requestTimestamps.push(timestamp);

          try {
            console.log(
              `Executing rate-limited function (${requestTimestamps.length}/${ratePerSecond} slots used)`
            );
            const result = await fn(...params);
            resolve(result);
          } catch (error) {
            console.error('Error in rate-limited function:', error);
            reject(error);
          }
        }
      } finally {
        console.log('Finished processing rate-limited queue');
        processing = false;
      }
    };

    // Return a function that will be rate limited
    return (...args) => {
      return new Promise((resolve, reject) => {
        queue.push({ params: args, resolve, reject });
        console.log(`Added item to rate limit queue (${queue.length} items waiting)`);
        processQueue(); // Start processing if not already running
      });
    };
  },

  async crawlTiktokData(keyword: string, affiliateId: string) {
    try {
      // Get affiliate information
      const affiliate = await strapi.documents('api::affiliate.affiliate').findOne({
        documentId: affiliateId,
      });

      if (!affiliate) {
        throw new Error(`Affiliate with ID ${affiliateId} not found`);
      }

      console.log(`Crawling TikTok data for keyword: ${keyword}, affiliate: ${affiliate.name}`);

      let parsedVideos = [];
      let totalVideos = 0;

      try {
        // First, try using the existing TikTok API client
        console.log('Attempting to use TikTok API client...');
        const result = await TiktokClient.search(keyword);
        // console.log('TikTok API search result:', result);

        if (result?.item_list) {
          console.log(`TikTok API returned ${result.item_list.length} videos`);
          totalVideos = result.item_list.length;

          // Parse and filter videos using existing logic
          parsedVideos = result.item_list
            // .filter((video) => {
            //   console.log('Checking video description:', video?.desc);
            //   if (!video?.desc) return false;
            //   const normalizedDesc = video.desc.toLowerCase().replace(/\s+/g, '');
            //   return normalizedDesc.includes(keyword.toLowerCase().replace(/\s+/g, ''));
            // })
            .map(parseTiktokVideo)
            .filter(Boolean);

          console.log(`Parsed ${parsedVideos.length} videos from TikTok API`);
        }
      } catch (apiError) {
        console.log('TikTok API failed, falling back to web crawler:', apiError.message);

        // Fallback to web crawler
        const crawler = new TikTokCrawler();
        try {
          const crawledVideos = await crawler.crawlTikTokSearch(keyword);
          totalVideos = crawledVideos.length;

          // Filter videos that contain the keyword
          parsedVideos = crawledVideos.filter((video) => {
            const normalizedDesc = video.description.toLowerCase().replace(/\s+/g, '');
            const normalizedTitle = video.title.toLowerCase().replace(/\s+/g, '');
            const normalizedKeyword = keyword.toLowerCase().replace(/\s+/g, '');

            return (
              normalizedDesc.includes(normalizedKeyword) ||
              normalizedTitle.includes(normalizedKeyword)
            );
          });

          console.log(
            `Web crawler found ${parsedVideos.length} relevant videos out of ${totalVideos}`
          );
        } finally {
          await crawler.close();
        }
      }

      // Save videos to database if we found any
      if (parsedVideos.length > 0) {
        // Get affiliate ID for linking
        const affiliateRecord = await strapi.query('api::affiliate.affiliate').findOne({
          where: { documentId: affiliateId },
        });

        if (affiliateRecord) {
          await batchUpsertSocialListening(parsedVideos, keyword, [affiliateRecord.id], 'video_id');
        }

        // Log the crawl activity
        await strapi.query('api::social-log.social-log').create({
          data: {
            keyword,
            platform: 'tiktok',
            affiliate: affiliateRecord?.id,
            fetched_at: new Date(),
            is_empty_result: parsedVideos.length === 0,
          },
        });
      }

      return {
        keyword,
        totalVideos,
        savedVideos: parsedVideos.length,
        videos: parsedVideos,
        method: parsedVideos.length > 0 ? 'api' : 'crawler',
      };
    } catch (error) {
      console.error('Error crawling TikTok data:', error);
      throw error;
    }
  },

  async crawlYoutubeData(keyword: string, affiliateId: string, detailLimit: number = 15) {
    try {
      // Get affiliate information
      const affiliate = await strapi.documents('api::affiliate.affiliate').findOne({
        documentId: affiliateId,
      });

      if (!affiliate) {
        throw new Error(`Affiliate with ID ${affiliateId} not found`);
      }

      console.log(`Crawling YouTube data for keyword: ${keyword}, affiliate: ${affiliate.name}`);

      let parsedVideos = [];
      let totalVideos = 0;

      try {
        // First, try using the existing YouTube API client
        console.log('Attempting to use YouTube API client...');
        const result = await YoutubeClient.search(keyword);

        if (result?.items) {
          console.log(`YouTube API returned ${result.items.length} videos`);
          totalVideos = result.items.length;

          // Get basic video data from search results
          const searchResults = result.items
            ? result.items.filter((video) => video.id?.videoId)
            : [];

          // Fetch detailed info for each video using Promise.all
          const videoIds = searchResults
            .map((item: any) => item?.id?.videoId)
            .filter(Boolean)
            .join(',');
          const resultDetails = (await YoutubeClient.getVideoInfo(videoIds)).items || [];

          const enhancedVideos = searchResults.map((item: any, index: number) => ({
            ...item,
            videoDetails: resultDetails[index] || {},
          }));

          // Parse videos using existing logic
          parsedVideos = enhancedVideos.map(parseYoutubeVideo).filter(Boolean);
          console.log(`Parsed ${parsedVideos.length} videos from YouTube API`);
        }
      } catch (apiError) {
        console.log('YouTube API failed, falling back to web crawler:', apiError.message);

        // Fallback to web crawler with detailed statistics
        const crawler = new YouTubeCrawler();
        try {
          // Use enhanced crawler to get detailed engagement metrics
          const crawledVideos = await crawler.crawlYouTubeSearchWithDetailedStats(
            keyword,
            detailLimit
          );
          totalVideos = crawledVideos.length;

          // Filter videos that contain the keyword
          parsedVideos = crawledVideos.filter((video) => {
            const normalizedDesc = video.description.toLowerCase().replace(/\s+/g, '');
            const normalizedTitle = video.title.toLowerCase().replace(/\s+/g, '');
            const normalizedKeyword = keyword.toLowerCase().replace(/\s+/g, '');

            return (
              normalizedDesc.includes(normalizedKeyword) ||
              normalizedTitle.includes(normalizedKeyword)
            );
          });

          console.log(
            `Web crawler found ${parsedVideos.length} relevant videos out of ${totalVideos}`
          );
        } finally {
          await crawler.close();
        }
      }

      // Save videos to database if we found any
      if (parsedVideos.length > 0) {
        // Get affiliate ID for linking
        const affiliateRecord = await strapi.query('api::affiliate.affiliate').findOne({
          where: { documentId: affiliateId },
        });

        if (affiliateRecord) {
          await batchUpsertSocialListening(parsedVideos, keyword, [affiliateRecord.id], 'video_id');
        }

        // Log the crawl activity
        await strapi.query('api::social-log.social-log').create({
          data: {
            keyword,
            platform: 'youtube',
            affiliate: affiliateRecord?.id,
            fetched_at: new Date(),
            is_empty_result: parsedVideos.length === 0,
          },
        });
      }

      return {
        keyword,
        totalVideos,
        savedVideos: parsedVideos.length,
        videos: parsedVideos,
        method: parsedVideos.length > 0 ? 'api' : 'crawler',
      };
    } catch (error) {
      console.error('Error crawling YouTube data:', error);
      throw error;
    }
  },
});

async function searchPosts(keyword: string, affiliate, platforms: string[]) {
  console.log('Search posts', keyword, platforms);

  // Fetch global settings to get refresh intervals
  const globalSettings = await strapi.entityService.findMany('api::global.global');

  // Get platform-specific refresh intervals
  const refreshDefaultSeconds = globalSettings?.refresh_social_after || 300;
  const refreshYoutubeSeconds = globalSettings?.refresh_youtube_interval || 600;
  const refreshTiktokSeconds = globalSettings?.refresh_tiktok_interval || 300;

  // Get the existing social listening data
  const socialListed = await strapi.query('api::social-listening.social-listening').findMany({
    filters: {
      affiliate: {
        id: affiliate.id,
      },
      keyword,
      platform: {
        $in: platforms,
      },
    },
  });

  console.log(`LOG-socialListed.length ${keyword} ${platforms}`, socialListed.length);

  // Get the most recent social log entries for this keyword grouped by platform
  const platformLogs = {};

  for (const platform of platforms) {
    const socialLog = await strapi.query('api::social-log.social-log').findMany({
      filters: {
        keyword,
        platform,
      },
      orderBy: { fetched_at: 'desc' },
      limit: 1,
    });

    platformLogs[platform] = socialLog.length ? socialLog[0] : null;
    console.log(`LOG-lastLog ${keyword} ${platform}`, platformLogs[platform]?.fetched_at || 'none');
  }

  const response = {
    platforms: [],
  };

  const linktos = await strapi.query('api::affiliate.affiliate').findMany({
    filters: {
      documentId: affiliate.documentId,
    },
  });

  const linkToIds = linktos.map((link) => link.id);
  console.log({ linkToIds });

  await Promise.all(
    platforms.map(async (platform) => {
      // Only process YouTube and TikTok platforms
      if (!['youtube', 'tiktok'].includes(platform)) {
        console.log(`Skipping platform: ${platform} (not supported)`);
        return;
      }

      const existingData = socialListed.filter((post) => post.platform === platform);
      const lastLog = platformLogs[platform];

      // Get the platform-specific refresh interval
      let refreshIntervalMs;
      switch (platform) {
        case 'youtube':
          refreshIntervalMs = refreshYoutubeSeconds * 1000;
          break;
        case 'tiktok':
          refreshIntervalMs = refreshTiktokSeconds * 1000;
          break;
        default:
          refreshIntervalMs = refreshDefaultSeconds * 1000;
      }

      // Determine if we should fetch new data based on platform-specific refresh interval
      const shouldFetchData =
        !lastLog ||
        new Date().getTime() - new Date(lastLog.fetched_at).getTime() > refreshIntervalMs;

      console.log(`shouldFetchData ${keyword} ${platform}`, shouldFetchData);

      // Check if we should fetch new data
      if (!existingData.length || shouldFetchData) {
        console.log(`Fetching new data for ${keyword} on ${platform}`);

        // Use both API and crawler methods simultaneously
        try {
          const apiPromise = fetchAndStoreVideos({
            keyword,
            linkToIds,
            platforms: [platform],
          });

          let crawlerPromise;
          if (platform === 'youtube') {
            crawlerPromise = strapi
              .service('api::social-listening.social-listening')
              .crawlYoutubeData(keyword, affiliate.documentId);
          } else if (platform === 'tiktok') {
            crawlerPromise = strapi
              .service('api::social-listening.social-listening')
              .crawlTiktokData(keyword, affiliate.documentId);
          }

          // Wait for both methods to complete
          const [apiResults, crawlerResults] = await Promise.all([apiPromise, crawlerPromise]);

          // Combine the results from both approaches
          if (platform === 'youtube') {
            // Get videos from both sources
            const apiVideos = apiResults.youtubeVideos || [];
            const crawlerVideos = crawlerResults?.videos || [];

            // Create a merged set of videos with unique video_ids
            const videoMap = new Map();

            // First add API videos to the map
            apiVideos.forEach((video) => {
              videoMap.set(video.video_id, video);
            });

            // Then add or update with crawler videos which generally have better engagement metrics
            crawlerVideos.forEach((video) => {
              if (videoMap.has(video.video_id)) {
                // If video exists from API, merge with priority to crawler metrics
                const existingVideo = videoMap.get(video.video_id);
                videoMap.set(video.video_id, {
                  ...existingVideo,
                  views: video.views > 0 ? video.views : existingVideo.views,
                  likes: video.likes > 0 ? video.likes : existingVideo.likes,
                  comments: video.comments > 0 ? video.comments : existingVideo.comments,
                  description:
                    video.description && video.description.length > existingVideo.description.length
                      ? video.description
                      : existingVideo.description,
                });
              } else {
                // If video is only from crawler, add it
                videoMap.set(video.video_id, video);
              }
            });

            // Convert map back to array for response
            const combinedVideos = Array.from(videoMap.values());

            response.platforms.push({
              name: 'youtube',
              posts: combinedVideos,
            });
          } else if (platform === 'tiktok') {
            // Get videos from both sources
            const apiVideos = apiResults.tiktokVideos || [];
            const crawlerVideos = crawlerResults?.videos || [];

            // Create a merged set of videos with unique video_ids
            const videoMap = new Map();

            // First add API videos to the map
            apiVideos.forEach((video) => {
              videoMap.set(video.video_id, video);
            });

            // Then add or update with crawler videos which generally have better engagement metrics
            crawlerVideos.forEach((video) => {
              if (videoMap.has(video.video_id)) {
                // If video exists from API, merge with priority to crawler metrics
                const existingVideo = videoMap.get(video.video_id);
                videoMap.set(video.video_id, {
                  ...existingVideo,
                  views: video.views > 0 ? video.views : existingVideo.views,
                  likes: video.likes > 0 ? video.likes : existingVideo.likes,
                  comments: video.comments > 0 ? video.comments : existingVideo.comments,
                  description:
                    video.description && video.description.length > existingVideo.description.length
                      ? video.description
                      : existingVideo.description,
                });
              } else {
                // If video is only from crawler, add it
                videoMap.set(video.video_id, video);
              }
            });

            // Convert map back to array for response
            const combinedVideos = Array.from(videoMap.values());

            response.platforms.push({
              name: 'tiktok',
              posts: combinedVideos,
            });
          }

          // Log this fetch
          const hasResults = response.platforms.find((p) => p.name === platform)?.posts?.length > 0;
          await strapi.query('api::social-log.social-log').create({
            data: {
              keyword,
              platform,
              affiliate: affiliate.id,
              fetched_at: new Date(),
              is_empty_result: !hasResults,
            },
          });
        } catch (error) {
          console.error(`Error fetching data for ${platform}:`, error);
          // In case of error, attempt to use just the API method as fallback
          try {
            const results = await fetchAndStoreVideos({
              keyword,
              linkToIds,
              platforms: [platform],
            });

            addResultsToPlatformResponse(platform, results, response);

            // Log this fetch
            const hasResults =
              response.platforms.find((p) => p.name === platform)?.posts?.length > 0;
            await strapi.query('api::social-log.social-log').create({
              data: {
                keyword,
                platform,
                affiliate: affiliate.id,
                fetched_at: new Date(),
                is_empty_result: !hasResults,
              },
            });
          } catch (fallbackError) {
            console.error(`Fallback error for ${platform}:`, fallbackError);
          }
        }
      } else if (existingData.length) {
        // Only use existing data if we shouldn't fetch new data
        console.log(`Using existing data for ${keyword} on ${platform}`);
        response.platforms.push({
          name: platform,
          posts: existingData,
        });
      }
    })
  );

  return response;
}

/**
 * Helper function to add platform-specific results to the response
 */
function addResultsToPlatformResponse(platform, results, response) {
  switch (platform) {
    case 'youtube':
      response.platforms.push({
        name: 'youtube',
        posts: results.youtubeVideos,
      });
      break;
    case 'tiktok':
      response.platforms.push({
        name: 'tiktok',
        posts: results.tiktokVideos,
      });
      break;
    case 'x':
      response.platforms.push({
        name: 'x',
        posts: results.xPosts,
      });
      break;
    case 'reddit':
      response.platforms.push({
        name: 'reddit',
        posts: results.redditPosts,
      });
      break;
  }
}

async function getAffiliate(documentId: string) {
  const affiliate = await strapi.documents('api::affiliate.affiliate').findOne({ documentId });
  if (!affiliate) throw new Error(`Affiliate with id ${documentId} not found`);
  return affiliate;
}

async function fetchAndStoreVideos({
  keyword,
  linkToIds,
  platforms = ['youtube', 'tiktok', 'x', 'reddit'],
}: {
  keyword: string;
  linkToIds: string[];
  platforms: string[];
}) {
  const platformHandlers = {
    youtube: async () => {
      try {
        const result = await YoutubeClient.search(keyword);

        // Get basic video data from search results
        const searchResults = result.items ? result.items.filter((video) => video.id?.videoId) : [];

        // Fetch detailed info for each video using Promise.all
        const videoIds = searchResults
          .map((item: any) => item?.id?.videoId)
          .filter(Boolean)
          .join(',');
        const resultDetails = (await YoutubeClient.getVideoInfo(videoIds)).items || [];
        console.log('resultDetails', resultDetails.length);
        console.log('searchResults', searchResults.length);

        const enhancedVideos = searchResults.map((item: any, index: number) => ({
          ...item,
          videoDetails: resultDetails[index] || {},
        }));

        return enhancedVideos.map(parseYoutubeVideo).filter(Boolean);
      } catch (err: any) {
        console.error('LOG-fetchAndStoreVideos YouTube Error', err.message);
        return [];
      }
    },
    tiktok: async () => {
      try {
        const result = await TiktokClient.search(keyword);
        // Check if the keyword contains multiple hashtags
        const keywordParts = keyword.split(' ').filter(Boolean);
        const isMultipleHashtags =
          keywordParts.length > 1 && keywordParts.every((part) => part.startsWith('#'));

        const globalConfig = await strapi.entityService.findMany('api::global.global');

        // Filter TikTok videos based on keyword logic
        console.log('LOG-fetchAndStoreVideos TikTok result', result?.item_list?.length || 0);
        return result?.item_list
          ?.filter((video) => {
            if (!video?.desc) return false;

            const normalizedDesc = video.desc.toLowerCase().replace(/\s+/g, '');

            if (isMultipleHashtags) {
              // If keyword contains multiple hashtags, match if ANY of the hashtags are in the description
              return keywordParts.some((part) =>
                normalizedDesc.includes(part.toLowerCase().replace(/\s+/g, ''))
              );
            } else {
              // global config id ignore check description

              const ignoreCheckDescription = globalConfig?.ignore_tiktok_des || '';
              if (
                ignoreCheckDescription &&
                _.intersection(
                  ignoreCheckDescription.split(',').map(parseInt),
                  linkToIds.map(parseInt)
                ).length > 0
              ) {
                return true;
              }
              // Original logic for single keywords
              return normalizedDesc.includes(keyword.toLowerCase().replace(/\s+/g, ''));
            }
          })
          ?.map(parseTiktokVideo)
          ?.filter(Boolean);
      } catch (err: any) {
        console.error('LOG-fetchAndStoreVideos TikTok Error', err.message);
        return [];
      }
    },
  };

  const results = await Promise.all(
    platforms.map(async (platform) => ({
      platform,
      videos: await (platformHandlers[platform]?.() || []),
    }))
  );

  // Store X posts
  await batchUpsertSocialListening(
    results.find((p) => p.platform == 'x')?.videos || [],
    keyword,
    linkToIds,
    'x_id'
  );

  // Store TikTok and YouTube videos
  const tiktokVideos = results.find((p) => p.platform == 'tiktok')?.videos || [];
  const youtubeVideos = results.find((p) => p.platform == 'youtube')?.videos || [];
  await batchUpsertSocialListening(
    [...tiktokVideos, ...youtubeVideos],
    keyword,
    linkToIds,
    'video_id'
  );

  // Store Reddit posts
  await batchUpsertSocialListening(
    results.find((p) => p.platform == 'reddit')?.videos || [],
    keyword,
    linkToIds,
    'post_id'
  );

  // Prepare and return the response object with actual post/video data
  const response = {
    youtubeVideos: results.find((p) => p.platform === 'youtube')?.videos || [],
    tiktokVideos: results.find((p) => p.platform === 'tiktok')?.videos || [],
    xPosts: results.find((p) => p.platform === 'x')?.videos || [],
    redditPosts: results.find((p) => p.platform === 'reddit')?.videos || [],
  };

  return response;
}

const parseYoutubeVideo = (video: any): IVideo => {
  if (!video) {
    return null;
  }
  // Extract basic info from search result
  const basicInfo = video.snippet || {};

  // Extract detailed info from videoDetails if available
  const detailedInfo = video.videoDetails || {};

  // Use videoId from either source
  const videoId = video?.id?.videoId || detailedInfo?.id || '';

  return {
    video_id: videoId,
    title: basicInfo?.title || detailedInfo?.title || '',
    description: detailedInfo?.snippet?.description || '',
    channel_id: basicInfo?.channelId || detailedInfo?.channelId || '',
    channel_title: basicInfo?.channelTitle || detailedInfo?.title || '',
    published_from: basicInfo?.publishTime || detailedInfo?.publishTime || '',
    thumbnail: basicInfo?.thumbnails?.high?.url || detailedInfo?.thumbnails?.high?.url || '',
    duration: formatDuration(detailedInfo?.contentDetails) || '0:00',
    video_link: videoId ? `https://www.youtube.com/watch?v=${videoId}` : '',
    link: videoId ? `https://www.youtube.com/watch?v=${videoId}` : '',
    views: Number(detailedInfo?.statistics?.viewCount) || 0,
    likes: Number(detailedInfo?.statistics?.likeCount) || 0,
    comments: Number(detailedInfo?.statistics?.commentCount) || 0,
    shares: Number(detailedInfo?.statistics?.shareCount) || 0,
    type: 'video',
    platform: 'youtube',
    is_displayed: false,
    is_verified: false,
    is_from_crawler: false, // This is from API
  };
};

const formatDuration = (contentDetails: any) => {
  if (!contentDetails || !contentDetails.duration) return '00:00';

  const isoDuration = contentDetails.duration;
  // Handle hours, minutes, and seconds in ISO 8601 duration format
  // Format: PT#H#M#S where # is a number, and H, M, S represent hours, minutes, seconds
  const match = isoDuration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
  if (!match) return '00:00'; // Default fallback

  const hours = match[1] ? parseInt(match[1], 10) : 0;
  const minutes = match[2] ? parseInt(match[2], 10) : 0;
  const seconds = match[3] ? parseInt(match[3], 10) : 0;

  // Convert hours to minutes if present
  const totalMinutes = hours * 60 + minutes;

  // Format as MM:SS or HH:MM:SS depending on length
  if (hours > 0) {
    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  } else {
    return `${String(totalMinutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  }
};

const parseTiktokVideo = (video: any): IVideo | null => {
  if (!video) {
    return null;
  }
  const parsedVideo = {
    video_id: video?.id || '',
    title: video?.desc || '',
    description: video?.desc || '', // TikTok does not provide a description in the same way as YouTube
    channel_id: video.author?.id || '',
    channel_title: video.author?.nickname || '',
    channel_avatar: video.author?.avatarThumb || '',
    published_from: video.createTime ? new Date(video.createTime * 1000).toISOString() : '',
    thumbnail: video?.video?.cover || '',
    duration: video?.video?.duration
      ? `${Math.floor(video?.video?.duration / 60)}:${video?.video?.duration % 60}`
      : '0:00',
    video_link:
      video?.author?.uniqueId && video?.id
        ? `https://www.tiktok.com/@${video?.author?.uniqueId}/video/${video?.id}`
        : '',
    link:
      video?.author?.uniqueId && video?.id
        ? `https://www.tiktok.com/@${video?.author?.uniqueId}/video/${video?.id}`
        : '',
    views: Number(video?.stats?.playCount) || 0,
    likes: Number(video?.stats?.diggCount) || 0,
    comments: Number(video?.stats?.commentCount) || 0,
    shares: Number(video?.stats?.shareCount) || 0,
    type: 'video',
    platform: 'tiktok',
    is_displayed: false,
    is_verified: false,
    is_from_crawler: false, // This is from API
  };
  return parsedVideo;
};

const parseXPost = (post: any): IXPost | null => {
  if (!post) {
    return null;
  }
  return {
    x_id: post.tweet_id || '',
    title: post.text ? post.text.split('\n')[0] : '',
    description: post.text || '',
    channel_id: post.user_info?.rest_id || '',
    thumbnail: post.media?.photo?.length > 0 ? post.media.photo[0].media_url_https : '',
    channel_title: post.user_info?.name || '',
    published_from: post.created_at ? new Date(post.created_at).toISOString() : '',
    photos: post.media?.photo?.map((photo: any) => photo.media_url_https)?.join(',') || '',
    link:
      post.user_info?.screen_name && post.tweet_id
        ? `https://x.com/${post.user_info.screen_name}/status/${post.tweet_id}`
        : '',
    type: 'post',
    views: post.views || 0,
    platform: 'x',
    is_verified: false,
  };
};

const parseRedditPost = (post: any): IRedditPost => {
  if (!post) {
    return null;
  }
  return {
    post_id: post.id || '',
    title: post.title || '',
    description: post.content?.text || '',
    published_from: post.creationDate ? new Date(post.creationDate).toISOString() : '',
    thumbnail: post.thumbnail?.url || '',
    link: post.url || '',
    comments: post.comments || 0,
    type: 'post',
    platform: 'reddit',
    channel_title: '',
    channel_id: '',
    views: post.score || 0,
    is_verified: false,
  };
};

async function batchUpsertSocialListening(
  videos: IVideo[],
  keyword: string,
  linkToIds: string[],
  uniqueField: string
) {
  const batchSize = 10000;

  for (let i = 0; i < videos.length; i += batchSize) {
    const batch = videos.slice(i, i + batchSize);
    // Ensure unique records in the batch by the uniqueField
    const uniqueVideos = Array.from(
      new Map(batch.map((video: any) => [video[uniqueField], video])).values()
    );

    try {
      await strapi.db.connection.transaction(async (trx) => {
        // Extract values for the uniqueField to use in the deletion query
        // const uniqueFieldValues = uniqueVideos.map((video) => video[uniqueField]).filter(Boolean);

        // if (uniqueFieldValues.length > 0) {
        //   // Step 1: Delete existing records that match the uniqueField values
        //   console.log(
        //     `Deleting existing records with ${uniqueField} values:`,
        //     uniqueFieldValues.length
        //   );
        //   await strapi.db
        //     .connection('social_listenings')
        //     .whereIn(uniqueField, uniqueFieldValues)
        //     .delete()
        //     .transacting(trx);
        // }

        // Step 2: Insert the new records
        const insertedVideos = await strapi.db
          .connection('social_listenings')
          .insert(
            uniqueVideos.map((video) => ({
              ...video,
              document_id: generateUniqueId(),
              created_at: new Date(),
              updated_at: new Date(),
              keyword,
            }))
          )
          .returning('id')
          .onConflict([uniqueField])
          .ignore()
          .transacting(trx);

        // Step 3: Link the `affiliate` to the `social_listenings` records
        const affiliateLinks = linkToIds.flatMap((affiliateId) =>
          insertedVideos.map((video) => ({
            social_listening_id: video.id,
            affiliate_id: affiliateId,
          }))
        );

        if (affiliateLinks.length > 0) {
          await strapi.db
            .connection('social_listenings_affiliate_lnk')
            .insert(affiliateLinks)
            .onConflict(['social_listening_id', 'affiliate_id'])
            .ignore()
            .transacting(trx);
        }
      });
    } catch (err: any) {
      console.error('LOG-batch upsert fail', err.message);
    }
  }
}

function generateUniqueId(length = 24) {
  return crypto
    .randomBytes(length)
    .toString('base64')
    .replace(/[^a-z0-9]/gi, '') // Remove non-alphanumeric chars
    .slice(0, length); // Trim to desired length
}
