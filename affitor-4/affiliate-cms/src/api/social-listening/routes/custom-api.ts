export default {
  routes: [
    {
      method: 'GET',
      path: '/social-listenings/:affiliateId',
      handler: 'social-listening.searchKeyword',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/social-listenings/transcript/:videoId',
      handler: 'social-listening.getTranscript',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/social-listenings/custom-transcript/:videoId',
      handler: 'social-listening.getCustomTranscript',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/social-listenings/tiktok-download/:videoId',
      handler: 'social-listening.getTiktokDownloadLink',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/social-listenings/tiktok-detail-cover/:videoId',
      handler: 'social-listening.getTiktokVideoDetailWithCover',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/social-listenings/crawl-tiktok',
      handler: 'social-listening.crawlTiktok',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/social-listenings/crawl-youtube',
      handler: 'social-listening.crawlYoutube',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
