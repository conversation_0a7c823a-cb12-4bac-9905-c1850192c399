/**
 * referrer service
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreService('api::referrer.referrer', ({ strapi }) => ({
  async register({ user }) {
    // Check if user exists
    if (!user || !user.id) {
      throw new Error('User is required to become a referrer');
    }

    // Check if user already has a referrer account
    const existingReferrer = await strapi.entityService.findMany('api::referrer.referrer', {
      filters: { user: user.id },
    });

    if (existingReferrer && existingReferrer.length > 0) {
      throw new Error('User is already registered as a referrer');
    }

    // Generate referral code based on username
    const referralCode = this.generateReferralCode(user.username || user.email);

    // Create new referrer linked to the user
    const referrer = await strapi.entityService.create('api::referrer.referrer', {
      data: {
        referral_code: referralCode,
        user: user.id,
        referrer_status: 'active',
        publishedAt: new Date(),
      },
    });

    return {
      success: true,
      data: referrer,
    };
  },

  // Helper method to generate a unique referral code
  generateReferralCode(identifier) {
    const prefix = identifier.substring(0, 3).toUpperCase();
    const random = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, '0');
    return `${prefix}-${random}`;
  },
}));
