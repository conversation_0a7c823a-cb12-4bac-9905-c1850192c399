export default {
  /**
   * Generate SSO response for Discourse (reverse flow only)
   * This should only be called when we have SSO parameters from Discourse
   */
  getSSOUrl: async (ctx) => {
    try {
      // Check if user is authenticated
      if (!ctx.state.user) {
        return ctx.unauthorized('Authentication required');
      }

      const user = ctx.state.user;
      const { sso, sig } = ctx.query;

      // Validate that we have SSO parameters (this is reverse flow only)
      if (!sso || !sig) {
        return ctx.badRequest('SSO parameters are required. This endpoint is for reverse SSO flow only.');
      }

      // Get the Discourse SSO service
      const discourseService = strapi.service('api::discourse.discourse-sso');

      // Generate SSO response URL
      const result = discourseService.generateSSOUrl(user, sso as string, sig as string);

      return ctx.send({
        success: true,
        data: result,
      });
    } catch (error) {
      strapi.log.error('Error generating Discourse SSO response:', error);
      return ctx.badRequest(error.message || 'Failed to generate SSO response');
    }
  },

  /**
   * Get Discourse login URL for forward flow
   * This returns the URL users should visit to initiate login on Discourse
   */
  getLoginUrl: async (ctx) => {
    try {
      // Get the Discourse SSO service
      const discourseService = strapi.service('api::discourse.discourse-sso');

      // Get the direct login URL
      const loginUrl = discourseService.getDiscourseLoginUrl();

      return ctx.send({
        success: true,
        data: {
          loginUrl,
          message: 'Visit this URL to login to Discourse. Discourse will then redirect back to our SSO endpoint.',
        },
      });
    } catch (error) {
      strapi.log.error('Error getting Discourse login URL:', error);
      return ctx.badRequest(error.message || 'Failed to get login URL');
    }
  },

  /**
   * Handle initial SSO request from Discourse
   * This endpoint is called when a user clicks "Log In" on Discourse
   */
  handleInitialSSORequest: async (ctx) => {
    try {
      const { sso, sig } = ctx.query;

      if (!sso || !sig) {
        return ctx.badRequest('Missing SSO parameters');
      }

      // Get the Discourse SSO service
      const discourseService = strapi.service('api::discourse.discourse-sso');

      // Handle the initial SSO request and get the login URL
      const loginUrl = discourseService.handleInitialSSORequest(sso as string, sig as string);

      // Redirect to the frontend login page with SSO parameters
      return ctx.redirect(loginUrl);
    } catch (error) {
      strapi.log.error('Error handling initial Discourse SSO request:', error);

      // Redirect to frontend with error status
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
      return ctx.redirect(
        `${frontendUrl}/authentication?error=${encodeURIComponent(error.message || 'Failed to process SSO request')}`
      );
    }
  },

  /**
   * Handle SSO return from Discourse
   * This endpoint is called by Discourse after successful authentication
   */
  handleSSOReturn: async (ctx) => {
    try {
      const { sso, sig } = ctx.query;

      if (!sso || !sig) {
        return ctx.badRequest('Missing SSO parameters');
      }

      // Get the Discourse SSO service
      const discourseService = strapi.service('api::discourse.discourse-sso');
      
      // Validate the SSO return
      const payload = discourseService.validateSSOReturn(sso, sig);

      // Log successful SSO return
      strapi.log.info('Discourse SSO return validated:', payload);

      // Redirect to frontend with success status
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
      return ctx.redirect(`${frontendUrl}/discourse/callback?status=success`);
    } catch (error) {
      strapi.log.error('Error handling Discourse SSO return:', error);
      
      // Redirect to frontend with error status
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
      return ctx.redirect(
        `${frontendUrl}/discourse/callback?status=error&message=${encodeURIComponent(error.message)}`
      );
    }
  },

  /**
   * Handle SSO logout from Discourse
   * This endpoint is called by Discourse when user logs out
   */
  handleSSOLogout: async (ctx) => {
    try {
      // Check if user is authenticated
      if (!ctx.state.user) {
        return ctx.unauthorized('Authentication required');
      }

      const user = ctx.state.user;

      // Get the Discourse SSO service
      const discourseService = strapi.service('api::discourse.discourse-sso');
      
      // Handle logout
      const result = await discourseService.handleSSOLogout(user);

      return ctx.send({
        success: true,
        data: result,
      });
    } catch (error) {
      strapi.log.error('Error handling Discourse SSO logout:', error);
      return ctx.badRequest(error.message || 'Failed to handle logout');
    }
  },

  /**
   * Sync user data with Discourse
   * This can be called when user profile is updated
   */
  // syncUser: async (ctx) => {
  //   try {
  //     // Check if user is authenticated
  //     if (!ctx.state.user) {
  //       return ctx.unauthorized('Authentication required');
  //     }

  //     const user = ctx.state.user;

  //     // Get the Discourse SSO service
  //     const discourseService = strapi.service('api::discourse.discourse-sso');
      
  //     // Sync user
  //     const result = await discourseService.syncUserWithDiscourse(user);

  //     return ctx.send({
  //       success: true,
  //       data: result,
  //     });
  //   } catch (error) {
  //     strapi.log.error('Error syncing user with Discourse:', error);
  //     return ctx.badRequest(error.message || 'Failed to sync user');
  //   }
  // },

  /**
   * Get Discourse configuration info (public endpoint)
   * Returns basic info about Discourse integration
   */
  getConfig: async (ctx) => {
    try {
      const discourseUrl = process.env.DISCOURSE_URL;
      
      if (!discourseUrl) {
        return ctx.badRequest('Discourse is not configured');
      }

      return ctx.send({
        success: true,
        data: {
          discourseUrl,
          ssoEnabled: !!process.env.DISCOURSE_SSO_SECRET,
          communityName: process.env.DISCOURSE_COMMUNITY_NAME || 'Community',
        },
      });
    } catch (error) {
      strapi.log.error('Error getting Discourse config:', error);
      return ctx.badRequest('Failed to get configuration');
    }
  },
};
