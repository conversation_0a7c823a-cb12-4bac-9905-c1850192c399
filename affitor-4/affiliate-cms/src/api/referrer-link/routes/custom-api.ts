/**
 * Custom routes for referrer-link
 */

export default {
  routes: [
    {
      method: 'POST',
      path: '/referrer-links/track-click',
      handler: 'referrer-link.trackClick',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/referrer-links/short/:shortLink',
      handler: 'referrer-link.findByShortLink',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/referrer-links/track-click-short',
      handler: 'referrer-link.trackClickByShortLink',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/referrer-links/generate-short-links',
      handler: 'referrer-link.generateShortLinksForExisting',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/referrer-links/available-pages',
      handler: 'referrer-link.getAvailablePages',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/referrer-links/update-all-totals',
      handler: 'referrer-link.updateAllReferrerTotals',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/referrer-links/sync-track-links',
      handler: 'referrer-link.syncTrackLinks',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
