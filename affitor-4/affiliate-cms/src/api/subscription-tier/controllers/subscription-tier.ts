/**
 * subscription-tier controller
 */

import { factories } from '@strapi/strapi';
import { Stripe } from 'stripe';

export default factories.createCoreController(
  'api::subscription-tier.subscription-tier',
  ({ strapi }) => ({
    // Get all published subscription tiers for pricing page
    async getPricingTiers(ctx) {
      try {
        const subscriptionService = strapi.service('api::subscription-tier.subscription-tier');
        const tiers = await subscriptionService.getAvailableTiers();

        // Filter out any sensitive or internal fields
        const sanitizedTiers = tiers.map((tier) => ({
          id: tier.id,
          name: tier.name,
          display_name: tier.display_name,
          description: tier.description,
          price: tier.price,
          request_limit: tier.request_limit,
          duration_days: tier.duration_days,
          features: tier.features,
          is_popular: tier.is_popular,
          stripe_recurring_interval: tier.stripe_recurring_interval || 'month',
          trial_days: tier.trial_days || 0,
        }));

        return sanitizedTiers;
      } catch (err) {
        ctx.throw(500, err);
      }
    },

    // Create checkout session for subscription
    async createCheckoutSession(ctx) {
      const { tierId } = ctx.request.body;

      if (!tierId) {
        return ctx.badRequest('Subscription tier ID is required');
      }

      try {
        const userId = ctx.state.user.id;
        const subscriptionService = strapi.service('api::subscription-tier.subscription-tier');

        const result = await subscriptionService.createCheckoutSession(userId, tierId);

        return result;
      } catch (err) {
        ctx.throw(500, err);
      }
    },

    // Confirm checkout after payment
    async confirmCheckoutSession(ctx) {
      const { sessionId } = ctx.params;

      if (!sessionId) {
        return ctx.badRequest('Session ID is required');
      }

      try {
        const userId = ctx.state.user.id;
        const subscriptionService = strapi.service('api::subscription-tier.subscription-tier');

        const result = await subscriptionService.confirmCheckoutSession(sessionId, userId, ctx);

        if (!result.success) {
          return ctx.badRequest(result.message || 'Failed to confirm checkout');
        }

        return {
          success: true,
          subscription: result.subscription,
        };
      } catch (err) {
        ctx.throw(500, err);
      }
    },

    // Get user's current subscription
    async getUserSubscription(ctx) {
      try {
        const userId = ctx.state.user.id;
        const subscriptionService = strapi.service('api::subscription-tier.subscription-tier');

        const userSubscription = await subscriptionService.getUserSubscription(userId);
        return userSubscription;
      } catch (err) {
        ctx.throw(500, err);
      }
    },

    // Cancel subscription
    async cancelSubscription(ctx) {
      try {
        const userId = ctx.state.user.id;
        const subscriptionService = strapi.service('api::subscription-tier.subscription-tier');

        const result = await subscriptionService.cancelSubscription(userId);

        return {
          success: true,
          cancelAtPeriodEnd: result.cancelAtPeriodEnd,
          cancelAt: result.cancelAt,
        };
      } catch (err) {
        ctx.throw(500, err);
      }
    },

    // Update subscription to new tier
    async updateSubscription(ctx) {
      const { subscriptionId, tierId } = ctx.request.body;

      if (!subscriptionId || !tierId) {
        return ctx.badRequest('Subscription ID and new tier ID are required');
      }

      try {
        const userId = ctx.state.user.id;
        const subscriptionService = strapi.service('api::subscription-tier.subscription-tier');

        const result = await subscriptionService.updateSubscription(userId, subscriptionId, tierId);

        return {
          success: true,
          subscription: result,
        };
      } catch (err) {
        ctx.throw(500, err);
      }
    },

    // Handle webhook events from Stripe
    async handleStripeWebhook(ctx) {
      const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
        apiVersion: '2025-04-30.basil',
      });

      const signature = ctx.request.headers['stripe-signature'];

      if (!signature) {
        return ctx.badRequest('Stripe signature is required');
      }

      try {
        const rawBody = ctx.request.body[Symbol.for('unparsedBody')];
        // Verify webhook signature
        const event = stripe.webhooks.constructEvent(
          rawBody,
          signature,
          process.env.STRIPE_WEBHOOK_SECRET
        ) as any;

        // Process the event
        const subscriptionService = strapi.service('api::subscription-tier.subscription-tier');
        await subscriptionService.processWebhookEvent(event);

        return { received: true };
      } catch (err) {
        console.error('Webhook error:', err);
        ctx.throw(400, `Webhook Error: ${err.message}`);
      }
    },

    async getComparePlans(ctx) {
      try {
        const subscriptionService = strapi.service('api::subscription-tier.subscription-tier');
        const tiers = await subscriptionService.getComparePlansData();

        return tiers;
      } catch (err) {
        ctx.throw(500, err);
      }
    },
  })
);
