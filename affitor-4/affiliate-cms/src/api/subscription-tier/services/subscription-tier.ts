/**
 * subscription-tier service
 */

import { factories } from '@strapi/strapi';
import Stripe from 'stripe';
import { errors } from '@strapi/utils';
const { NotFoundError } = errors;

export default factories.createCoreService(
  'api::subscription-tier.subscription-tier',
  ({ strapi }) => ({
    // Get all active subscription tiers
    async getAvailableTiers() {
      return (await strapi.entityService.findMany('api::subscription-tier.subscription-tier', {
        sort: { price: 'asc' },
        filters: {
          publishedAt: { $ne: null },
        },
      })) as any[];
    },

    // Get a specific tier by ID
    async getTierById(id: number) {
      try {
        return (await strapi.entityService.findOne(
          'api::subscription-tier.subscription-tier',
          id,
          {}
        )) as any;
      } catch (error) {
        console.error(`Error finding tier with ID ${id}:`, error);
        return null;
      }
    },

    // Calculate unit amount in cents based on price and interval
    calculateUnitAmountInCents(price: number, interval: string): number {
      // Calculate price based on interval
      const unitAmount = Number(price);
      let unitAmountInCents = Math.round(unitAmount * 100); // Convert to cents first

      if (interval === 'year') {
        unitAmountInCents *= 12; // Apply yearly multiplication to the cents amount
      }

      if (interval === 'quarter') {
        unitAmountInCents *= 3; // Apply quarterly multiplication to the cents amount
      }

      return unitAmountInCents;
    },

    // Create or get Stripe product and price for a tier
    async getOrCreateStripePricing(tier: any): Promise<{ productId: string; priceId: string }> {
      const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
        apiVersion: '2025-04-30.basil',
      });

      let productId = tier.stripe_product_id;
      let priceId = tier.stripe_price_id;
      let updated = false;

      // Create product if not exists
      if (!productId) {
        const product = await stripe.products.create({
          name: tier.name,
          description: tier.description || `${tier.name} subscription tier`,
          metadata: {
            tierId: tier.id.toString(),
          },
        });

        productId = product.id;
        updated = true;
      }

      // Create price if not exists
      if (!priceId) {
        const unitAmountInCents = this.calculateUnitAmountInCents(
          tier.price,
          tier.stripe_recurring_interval
        );

        const price = await stripe.prices.create({
          product: productId,
          unit_amount: unitAmountInCents,
          currency: 'usd',
          recurring: {
            interval: (tier.stripe_recurring_interval === 'year' ? 'year' : 'month') as any,
            interval_count: tier.stripe_recurring_interval === 'quarter' ? 3 : 1,
          },
          metadata: {
            tierId: tier.id.toString(),
          },
        });

        priceId = price.id;
        updated = true;
      }

      // Update tier with Stripe IDs if they were created
      if (updated) {
        await strapi.entityService.update('api::subscription-tier.subscription-tier', tier.id, {
          data: {
            stripe_product_id: productId,
            stripe_price_id: priceId,
          },
        });
      }

      return { productId, priceId };
    },

    // Create or get Stripe customer for a user
    async getOrCreateStripeCustomer(userId: number): Promise<string> {
      const user = (await strapi.entityService.findOne(
        'plugin::users-permissions.user',
        userId
      )) as any;

      if (!user) {
        throw new Error(`User with ID ${userId} not found`);
      }

      const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
        apiVersion: '2025-04-30.basil',
      });

      // Create or get Stripe customer
      let stripeCustomerId = user.stripe_customer_id;
      if (!stripeCustomerId) {
        const customer = await stripe.customers.create({
          email: user.email,
          name: user.username,
          metadata: {
            userId: userId.toString(),
          },
        });

        stripeCustomerId = customer.id;

        // Update user with Stripe customer ID
        await strapi.entityService.update('plugin::users-permissions.user', userId, {
          data: {
            stripe_customer_id: stripeCustomerId,
          },
        });
      }

      return stripeCustomerId;
    },

    // Create a checkout session for subscription
    async createCheckoutSession(userId: number, tierId: number) {
      try {
        const tier = await this.getTierById(tierId);
        if (!tier) {
          throw new NotFoundError(`Subscription tier with ID ${tierId} not found`);
        }

        console.log('Creating checkout session for user:', userId, 'price:', Number(tier.price));

        // Check if the tier is a free tier (price = 0)
        if (Number(tier.price) === 0) {
          console.log('Free tier detected, bypassing Stripe checkout');

          // Get the user to check if they already have a stripe_customer_id
          const user = (await strapi.entityService.findOne(
            'plugin::users-permissions.user',
            userId
          )) as any;

          const stripeCustomerId = user?.stripe_customer_id || null;

          const currentPeriodEnd = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(); // 30 days from now

          // Create a transaction record for the free tier
          const transaction: any = await strapi.entityService.create(
            'api::transaction.transaction',
            {
              data: {
                user: userId,
                subscription_tier: tier.id,
                amount: 0,
                currency: 'USD',
                payment_status: 'completed', // Mark as completed immediately
                payment_method: 'free',
                transaction_date: new Date().toISOString(),
                payment_details: { free_tier: true },
                stripe_customer_id: stripeCustomerId,
                current_period_start: new Date().toISOString(),
                current_period_end: currentPeriodEnd,
                auto_renew: true,
                publishedAt: new Date().toISOString(),
              },
            }
          );

          // Update user's subscription immediately
          const userTrackingService = strapi.service(
            'api::user-tracking-request.user-tracking-request'
          ) as any;

          await userTrackingService.updateSubscription(
            userId,
            tier,
            tier.request_limit,
            currentPeriodEnd,
            transaction
          );

          // Update affiliate ref rates if applicable
          await this.updateUserAffiliateRefRates(userId, tier);

          return {
            transaction,
            sessionId: null,
            checkoutUrl: null,
            freeSubscription: true,
          };
        }

        // Continue with regular paid tier flow...
        // Create or get Stripe customer ID
        const stripeCustomerId = await this.getOrCreateStripeCustomer(userId);

        // Get or create Stripe pricing
        const { priceId } = await this.getOrCreateStripePricing(tier);

        // Initialize Stripe
        const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
          apiVersion: '2025-04-30.basil',
        });

        // Create a checkout session with allow_promotion_codes set to true
        const session = (await stripe.checkout.sessions.create({
          customer: stripeCustomerId,
          payment_method_types: ['card'],
          line_items: [
            {
              price: priceId,
              quantity: 1,
            },
          ],
          mode: 'subscription',
          allow_promotion_codes: true, // This enables the coupon field in checkout
          billing_address_collection: 'auto', // Collect billing address
          payment_method_collection: 'always', // Always collect payment method
          ui_mode: 'hosted', // Use the hosted UI mode
          success_url: `${process.env.FRONTEND_URL}/profile/upgrade/success?session_id={CHECKOUT_SESSION_ID}`,
          cancel_url: `${process.env.FRONTEND_URL}/profile/upgrade/cancel`,
          metadata: {
            userId: userId.toString(),
            tierId: tier.id.toString(),
          },
          subscription_data: {
            metadata: {
              userId: userId.toString(),
              tierId: tier.id.toString(),
            },
          },
          // The custom_text option helps highlight the coupon option
          custom_text: {
            submit: {
              message: 'You can apply a coupon code before completing your purchase.',
            },
          },
        })) as any;

        // Create transaction record
        const transaction: any = await strapi.entityService.create('api::transaction.transaction', {
          data: {
            user: userId,
            subscription_tier: tier.id,
            amount:
              this.calculateUnitAmountInCents(tier.price, tier.stripe_recurring_interval) / 100,
            currency: 'USD',
            payment_status: 'pending',
            payment_method: 'stripe',
            transaction_date: new Date().toISOString(),
            payment_details: {},
            stripe_checkout_session: session.id,
            stripe_customer_id: stripeCustomerId,
            stripe_price_id: priceId,
            auto_renew: true,
            publishedAt: new Date().toISOString(),
          },
        });

        console.log('Transaction created:', transaction);

        return {
          transaction,
          sessionId: session.id,
          checkoutUrl: session.url as string,
        };
      } catch (error) {
        console.error('Error creating checkout session:', error);
        throw error;
      }
    },

    // Handle extra steps after subscription activation (non-blocking)
    async handlePostSubscriptionSteps(
      userId: number,
      subscriptionTier: any,
      transaction: any,
      ctx: any = null
    ): Promise<void> {
      // let add check transaction is already processed post subscription
      if (transaction.is_checked_subscription) {
        return;
      }

      // Create commission for referral if applicable (non-blocking)
      try {
        const user = await strapi.entityService.findOne('plugin::users-permissions.user', userId);
        if (user) {
          const commissionService = strapi.service(
            'api::referral-commission.referral-commission'
          ) as any;
          await commissionService.createCommission(subscriptionTier, user);
          console.log('Commission created successfully for user:', userId);
        }
      } catch (commissionError) {
        console.error('Error creating commission (non-blocking):', commissionError);
      }

      // Update affiliate ref rates if applicable (non-blocking)
      try {
        await this.updateUserAffiliateRefRates(userId, subscriptionTier);
        console.log('Affiliate ref rates updated successfully for user:', userId);
      } catch (affiliateError) {
        console.error('Error updating affiliate ref rates (non-blocking):', affiliateError);
      }

      // Update referrer link conversion rates (non-blocking)
      try {
        const referralUrl = ctx?.state?.referralUrl || ctx?.cookies?.get('referral_url') || null;
        const user = await strapi.entityService.findOne('plugin::users-permissions.user', userId);
        console.log('Referral URL in confirm checkout:', referralUrl);
        if (referralUrl) {
          await strapi
            .service('api::referrer-link.referrer-link')
            .trackConversionByUrl(
              referralUrl,
              user,
              transaction.amount,
              subscriptionTier.display_name
            );
          console.log('Referral conversion tracked successfully for user:', userId);
        }
      } catch (referralError) {
        console.error('Error tracking referral conversion (non-blocking):', referralError);
      }

      // Mark transaction as checked for subscription
      await strapi.entityService.update('api::transaction.transaction', transaction.id, {
        data: {
          is_checked_subscription: true,
        },
      });
    },

    // Confirm checkout session after user completes payment
    async confirmCheckoutSession(sessionId: string, userId: number, ctx: any = null) {
      try {
        const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
          apiVersion: '2025-04-30.basil',
        });

        // Retrieve session
        const session = (await stripe.checkout.sessions.retrieve(sessionId, {
          expand: ['subscription'],
        })) as any;

        // Check if session is completed and belongs to this user
        if (session.status !== 'complete') {
          return { success: false, message: 'Checkout not completed' };
        }

        if (session.metadata?.userId !== userId.toString()) {
          return { success: false, message: 'Session does not belong to this user' };
        }

        // Find transaction by checkout session ID
        const transactions = await strapi.entityService.findMany('api::transaction.transaction', {
          filters: {
            stripe_checkout_session: sessionId,
          },
          populate: { subscription_tier: true, user: true },
          limit: 1,
        });

        if (!transactions || transactions.length === 0) {
          return { success: false, message: 'Transaction not found' };
        }

        const transaction: any = transactions[0];

        const subscriptionId = session.subscription.id;
        const subscription = await stripe.subscriptions.retrieve(subscriptionId);

        if (!subscription) {
          return { success: false, message: 'No subscription created' };
        }

        // Update transaction with subscription info
        const updatedTransaction: any = await strapi.entityService.update(
          'api::transaction.transaction',
          transaction.id,
          {
            data: {
              stripe_subscription_id: subscription.id,
              payment_status: 'completed',
              current_period_start: new Date(
                subscription.items.data[0].current_period_start * 1000
              ).toISOString(),
              current_period_end: new Date(
                subscription.items.data[0].current_period_end * 1000
              ).toISOString(),
            },
          }
        );

        // Update user's subscription in tracking
        const userTrackingService = strapi.service(
          'api::user-tracking-request.user-tracking-request'
        ) as any;
        const subscriptionTier =
          typeof transaction.subscription_tier === 'number'
            ? await this.getTierById(transaction.subscription_tier)
            : (transaction.subscription_tier as unknown as any);

        // log subscriptionTier
        if (subscriptionTier) {
          await userTrackingService.updateSubscription(
            userId,
            subscriptionTier,
            subscriptionTier.request_limit,
            new Date(subscription.items.data[0].current_period_end * 1000).toISOString(),
            transaction
          );

          // Handle post-subscription steps (non-blocking)
          await this.handlePostSubscriptionSteps(userId, subscriptionTier, updatedTransaction, ctx);
        }

        return {
          success: true,
          subscription: {
            id: subscription.id,
            status: subscription.status,
            currentPeriodEnd: subscription.items.data[0].current_period_end,
            cancelAtPeriodEnd: subscription.cancel_at_period_end,
          },
          transaction: updatedTransaction,
        };
      } catch (error) {
        console.error('Error confirming checkout session:', error);
        throw error;
      }
    },

    // Process webhook events from Stripe
    async processWebhookEvent(event: any): Promise<boolean> {
      console.log(`Processing Stripe webhook event: ${event.type}`);

      switch (event.type) {
        case 'checkout.session.completed':
          await this.handleCheckoutSessionCompleted(event.data.object as any);
          break;

        case 'customer.subscription.created':
          await this.handleSubscriptionCreated(event.data.object as any);
          break;

        case 'customer.subscription.updated':
          await this.handleSubscriptionUpdated(event.data.object as any);
          break;

        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event.data.object as any);
          break;

        case 'invoice.payment_succeeded':
          await this.handleInvoicePaymentSucceeded(event.data.object as any);
          break;

        case 'invoice.payment_failed':
          await this.handleInvoicePaymentFailed(event.data.object as any);
          break;

        default:
          console.log(`Unhandled event type: ${event.type}`);
      }

      return true;
    },

    // Helper function to send notifications to Slack
    async sendSlackNotification(message: string, blocks?: any[]): Promise<void> {
      try {
        // Get global settings to retrieve Slack webhook URL
        const globalSettings = await strapi.entityService.findMany('api::global.global');

        const webhookUrl = globalSettings?.slack_webhook_url;

        if (!webhookUrl) {
          console.log('Slack webhook URL not configured in global settings');
          return;
        }

        // Prepare the payload
        const payload = {
          text: message,
          ...(blocks ? { blocks } : {}),
        };

        // Send the notification to Slack
        const response = await fetch(webhookUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        });

        if (!response.ok) {
          console.error(`Failed to send Slack notification: ${response.statusText}`);
        }
      } catch (error) {
        console.error('Error sending Slack notification:', error);
      }
    },

    // Handle checkout.session.completed event
    async handleCheckoutSessionCompleted(session: any): Promise<void> {
      if (session.mode !== 'subscription') return;

      try {
        // Check if this session has already been processed
        const existingCompletedTransactions = await strapi.entityService.findMany(
          'api::transaction.transaction',
          {
            filters: {
              stripe_checkout_session: session.id,
              payment_status: 'completed',
            },
          }
        );

        if (existingCompletedTransactions && existingCompletedTransactions.length > 0) {
          console.log(`Checkout session ${session.id} has already been processed in webhook`);
          return;
        }

        // Get the transaction for this checkout session
        const transactions = (await strapi.entityService.findMany('api::transaction.transaction', {
          filters: { stripe_checkout_session: session.id },
          populate: { subscription_tier: true, user: true },
        })) as any[];

        if (!transactions || transactions.length === 0) {
          console.log(`No transaction found for checkout session ${session.id}`);
          return;
        }

        const transaction = transactions[0];

        // Only update if subscription exists and transaction not already processed
        if (session.subscription && transaction.payment_status === 'pending') {
          // Update transaction with subscription info
          await strapi.entityService.update('api::transaction.transaction', transaction.id, {
            data: {
              stripe_subscription_id: session.subscription,
              payment_details: session,
              stripe_invoice_id: session.invoice,
              payment_status: 'completed',
            },
          });

          // Update user's subscription in tracking
          const userTrackingService = strapi.service(
            'api::user-tracking-request.user-tracking-request'
          ) as any;
          const subscriptionTier =
            typeof transaction.subscription_tier === 'number'
              ? await this.getTierById(transaction.subscription_tier)
              : (transaction.subscription_tier as unknown as any);
          const userId =
            typeof transaction.user === 'number'
              ? transaction.user
              : (transaction.user as { id: number }).id;
          if (userId && subscriptionTier) {
            await userTrackingService.updateSubscription(
              userId,
              subscriptionTier,
              subscriptionTier.request_limit,
              null,
              transaction
            );

            // Handle post-subscription steps (non-blocking)
            // await this.handlePostSubscriptionSteps(userId, subscriptionTier, transaction);

            // Get user details for the notification
            const user = await strapi.entityService.findOne(
              'plugin::users-permissions.user',
              userId
            );

            // Send Slack notification about the successful subscription
            console.log(
              `Sending Slack notification for new subscription: ${user.username || user.email}`
            );
            if (user) {
              const amount = transaction.amount || subscriptionTier.price;
              const currency = transaction.currency || 'USD';

              await this.sendSlackNotification(
                `🎉 New subscription! ${user.username || user.email} has subscribed to the ${subscriptionTier.name} plan for ${amount} ${currency}`,
                [
                  {
                    type: 'section',
                    text: {
                      type: 'mrkdwn',
                      text: `*New Subscription*\n*User:* ${user.username || user.email}\n*Plan:* ${subscriptionTier.name}\n*Amount:* ${amount} ${currency}\n*Transaction ID:* ${transaction.id}`,
                    },
                  },
                  {
                    type: 'context',
                    elements: [
                      {
                        type: 'mrkdwn',
                        text: `Subscription ID: ${session.subscription} | Checkout Session: ${session.id}`,
                      },
                    ],
                  },
                ]
              );
            }
          }

          console.log(
            `Updated transaction ${transaction.id} with subscription ${session.subscription}`
          );
        }
      } catch (error) {
        console.error('Error handling checkout session completed:', error);
      }
    },

    // Handle customer.subscription.created event
    async handleSubscriptionCreated(subscription: any): Promise<void> {
      try {
        console.log(`New subscription created: ${subscription.id}`);

        // This is typically handled by checkout.session.completed
        // But we can add additional logic here if needed
      } catch (error) {
        console.error('Error handling subscription created:', error);
      }
    },

    // Handle customer.subscription.updated event
    async handleSubscriptionUpdated(subscription: any): Promise<void> {
      try {
        console.log(`Subscription updated: ${subscription.id}, status: ${subscription.status}`);

        // Find transaction with this subscription ID
        const transactions = (await strapi.entityService.findMany('api::transaction.transaction', {
          filters: {
            stripe_subscription_id: subscription.id,
            parent_transaction: null,
            publishedAt: { $ne: null },
          },
          sort: { createdAt: 'desc' },
          limit: 1,
          populate: { user: true, subscription_tier: true },
        })) as any[];

        if (!transactions || transactions.length === 0) {
          console.log(`No transaction found for subscription ${subscription.id}`);
          return;
        }

        const parentTransaction = transactions[0];

        // Map Stripe status to our payment status
        let paymentStatus = parentTransaction.payment_status;
        switch (subscription.status) {
          case 'active':
            paymentStatus = 'completed';
            break;
          case 'past_due':
            paymentStatus = 'past_due';
            break;
          case 'unpaid':
            paymentStatus = 'unpaid';
            break;
          case 'canceled':
            paymentStatus = 'cancelled';
            break;
          default:
            // Keep existing status
            break;
        }

        // Extract needed values from parent transaction
        const userId =
          typeof parentTransaction.user === 'number'
            ? parentTransaction.user
            : (parentTransaction.user as { id: number }).id;

        const tierId =
          typeof parentTransaction.subscription_tier === 'number'
            ? parentTransaction.subscription_tier
            : (parentTransaction.subscription_tier as any).id;

        // Get subscription tier details
        const subscriptionTier =
          typeof parentTransaction.subscription_tier === 'number'
            ? await this.getTierById(parentTransaction.subscription_tier)
            : (parentTransaction.subscription_tier as any);

        // Create a new transaction for the subscription update
        const childTransaction = await strapi.entityService.create('api::transaction.transaction', {
          data: {
            user: userId,
            subscription_tier: tierId,
            amount: parentTransaction.amount,
            currency: parentTransaction.currency,
            payment_status: paymentStatus,
            payment_method: parentTransaction.payment_method,
            transaction_date: new Date().toISOString(),
            payment_details: subscription,
            stripe_checkout_session: null,
            stripe_customer_id: subscription.customer,
            stripe_subscription_id: subscription.id,
            stripe_price_id: parentTransaction.stripe_price_id,
            current_period_start: new Date(
              subscription.items.data[0].current_period_start * 1000
            ).toISOString(),
            current_period_end: new Date(
              subscription.items.data[0].current_period_end * 1000
            ).toISOString(),
            auto_renew: !subscription.cancel_at_period_end,
            parent_transaction: parentTransaction.id,
            publishedAt: new Date().toISOString(),
          },
        });

        console.log('Created child transaction:', childTransaction);

        // Handle subscription status changes
        if (subscription.status === 'active') {
          // Ensure user tracking reflects active subscription
          const userTrackingService = strapi.service(
            'api::user-tracking-request.user-tracking-request'
          ) as any;

          if (userId && subscriptionTier) {
            await userTrackingService.updateSubscription(
              userId,
              tierId,
              subscriptionTier.request_limit
            );

            // Handle post-subscription steps (non-blocking)
            // await this.handlePostSubscriptionSteps(userId, subscriptionTier, childTransaction);
          }
        } else if (subscription.status === 'canceled' || subscription.status === 'unpaid') {
          // Cancel user benefits if subscription is no longer active
          const userTrackingService = strapi.service(
            'api::user-tracking-request.user-tracking-request'
          ) as any;

          if (userId) {
            await userTrackingService.cancelSubscription(userId);
          }
        }
      } catch (error) {
        console.error('Error handling subscription updated:', error);
      }
    },

    // Handle customer.subscription.deleted event
    async handleSubscriptionDeleted(subscription: any): Promise<void> {
      try {
        console.log(`Subscription deleted: ${subscription.id}`);

        // Find transaction with this subscription ID
        const transactions = (await strapi.entityService.findMany('api::transaction.transaction', {
          filters: {
            stripe_subscription_id: subscription.id,
            publishedAt: { $ne: null },
          },
          sort: { createdAt: 'desc' },
          limit: 1,
          populate: { user: true },
        })) as any[];

        if (!transactions || transactions.length === 0) {
          console.log(`No transaction found for subscription ${subscription.id}`);
          return;
        }

        const transaction = transactions[0];

        // Update transaction
        await strapi.entityService.update('api::transaction.transaction', transaction.id, {
          data: {
            payment_status: 'cancelled',
            cancellation_date: new Date().toISOString(),
            auto_renew: false,
          },
        });

        // Remove subscription benefits
        const userTrackingService = strapi.service(
          'api::user-tracking-request.user-tracking-request'
        ) as any;
        const userId =
          typeof transaction.user === 'number'
            ? transaction.user
            : (transaction.user as { id: number }).id;

        if (userId) {
          await userTrackingService.cancelSubscription(userId);
        }
      } catch (error) {
        console.error('Error handling subscription deleted:', error);
      }
    },

    // Handle invoice.payment_succeeded event
    async handleInvoicePaymentSucceeded(invoice: any): Promise<void> {
      console.log(`Invoice payment succeeded: ${invoice.id}`);
      if (!invoice.subscription) return;

      try {
        console.log(`Payment succeeded for subscription: ${invoice.subscription}`);

        // Find transaction with this subscription ID
        const transactions = (await strapi.entityService.findMany('api::transaction.transaction', {
          filters: { stripe_subscription_id: invoice.subscription, publishedAt: { $ne: null } },
          sort: { createdAt: 'desc' },
          limit: 1,
          populate: { user: true, subscription_tier: true },
        })) as any[];

        if (!transactions || transactions.length === 0) {
          console.log(`No transaction found for subscription ${invoice.subscription}`);
          return;
        }

        const transaction = transactions[0];

        const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
          apiVersion: '2025-04-30.basil',
        });

        // Get subscription details
        const subscription = (await stripe.subscriptions.retrieve(invoice.subscription)) as any;

        // Create a new transaction for recurring payment if this is not the initial payment
        if (invoice.billing_reason === 'subscription_cycle') {
          const startDate = new Date(subscription.current_period_start * 1000);
          const endDate = new Date(subscription.current_period_end * 1000);

          const userId =
            typeof transaction.user === 'number'
              ? transaction.user
              : (transaction.user as { id: number }).id;

          const tierId =
            typeof transaction.subscription_tier === 'number'
              ? transaction.subscription_tier
              : (transaction.subscription_tier as any).id;

          // Create a new transaction for this billing cycle
          await strapi.entityService.create('api::transaction.transaction', {
            data: {
              user: userId,
              subscription_tier: tierId,
              amount: Number(invoice.amount_paid) / 100, // Convert from cents
              currency:
                invoice.currency === 'usd' || invoice.currency === 'eur'
                  ? (invoice.currency.toUpperCase() as 'USD' | 'EUR')
                  : 'USD',
              payment_status: 'completed',
              payment_method: 'stripe',
              transaction_date: new Date().toISOString(),
              payment_details: {},
              stripe_checkout_session: null,
              stripe_customer_id: invoice.customer,
              stripe_subscription_id: invoice.subscription,
              stripe_invoice_id: invoice.id,
              stripe_price_id: transaction.stripe_price_id,
              current_period_start: startDate.toISOString(),
              current_period_end: endDate.toISOString(),
              auto_renew: !subscription.cancel_at_period_end,
              publishedAt: new Date().toISOString(),
            },
          });
        } else {
          // Update original transaction with invoice details
          await strapi.entityService.update('api::transaction.transaction', transaction.id, {
            data: {
              stripe_invoice_id: invoice.id,
              payment_status: 'completed',
            },
          });
        }

        // Make sure user has active subscription
        const userTrackingService = strapi.service(
          'api::user-tracking-request.user-tracking-request'
        ) as any;

        const userId =
          typeof transaction.user === 'number'
            ? transaction.user
            : (transaction.user as { id: number }).id;

        const subscriptionTier =
          typeof transaction.subscription_tier === 'number'
            ? await this.getTierById(transaction.subscription_tier)
            : (transaction.subscription_tier as any);

        const tierId =
          typeof transaction.subscription_tier === 'number'
            ? transaction.subscription_tier
            : subscriptionTier.id;

        if (userId && tierId && subscriptionTier) {
          await userTrackingService.updateSubscription(
            userId,
            tierId,
            subscriptionTier.request_limit
          );
        }
      } catch (error) {
        console.error('Error handling invoice payment succeeded:', error);
      }
    },

    // Handle invoice.payment_failed event
    async handleInvoicePaymentFailed(invoice: any): Promise<void> {
      if (!invoice.subscription) return;

      try {
        console.log(`Payment failed for subscription: ${invoice.subscription}`);

        // Find transaction with this subscription ID
        const transactions = (await strapi.entityService.findMany('api::transaction.transaction', {
          filters: { stripe_subscription_id: invoice.subscription, publishedAt: { $ne: null } },
          sort: { createdAt: 'desc' },
          limit: 1,
          populate: { user: true },
        })) as any[];

        if (!transactions || transactions.length === 0) {
          console.log(`No transaction found for subscription ${invoice.subscription}`);
          return;
        }

        const transaction = transactions[0];

        // Update transaction
        await strapi.entityService.update('api::transaction.transaction', transaction.id, {
          data: {
            payment_status: 'failed',
            stripe_invoice_id: invoice.id,
          },
        });

        // Could send a notification to the user about payment failure
        const userId =
          typeof transaction.user === 'number'
            ? transaction.user
            : (transaction.user as { id: number }).id;

        console.log(`Payment failed for user ${userId}`);
      } catch (error) {
        console.error('Error handling invoice payment failed:', error);
      }
    },

    // Cancel a subscription
    async cancelSubscription(userId: number): Promise<{
      cancelAt: Date | null;
      cancelAtPeriodEnd: boolean;
      willEndImmediately: boolean;
    }> {
      try {
        const user: any = await strapi.entityService.findOne(
          'plugin::users-permissions.user',
          userId,
          {
            populate: {
              user_tracking_request: {
                populate: ['subscription_tier', 'transaction'],
              },
            },
          }
        );
        if (!user) {
          throw new Error(`User with ID ${userId} not found`);
        }

        const transaction = user.user_tracking_request.transaction;

        if (!transaction.stripe_subscription_id) {
          await strapi.entityService.update('api::transaction.transaction', transaction.id, {
            data: {
              auto_renew: false,
              cancellation_date: new Date().toISOString(),
            },
          });
          return {
            cancelAt: new Date(),
            cancelAtPeriodEnd: true,
            willEndImmediately: false,
          };
        }
        const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
          apiVersion: '2025-04-30.basil',
        });

        // Cancel subscription immediately or at period end
        const cancelAtPeriodEnd = true; // Could be a parameter if you want to support immediate cancellation

        let result: any;
        if (cancelAtPeriodEnd) {
          // Cancel at the end of the current period
          result = (await stripe.subscriptions.update(transaction.stripe_subscription_id, {
            cancel_at_period_end: true,
          })) as any;
        } else {
          // Cancel immediately
          result = (await stripe.subscriptions.cancel(transaction.stripe_subscription_id)) as any;
        }

        // Update transaction
        await strapi.entityService.update('api::transaction.transaction', transaction.id, {
          data: {
            auto_renew: false,
            cancellation_date: new Date().toISOString(),
            // Only mark as cancelled if cancelled immediately
            payment_status: cancelAtPeriodEnd ? transaction.payment_status : 'cancelled',
          },
        });

        // If cancelling immediately, update user tracking
        if (!cancelAtPeriodEnd) {
          const userTrackingService = strapi.service(
            'api::user-tracking-request.user-tracking-request'
          ) as any;
          await userTrackingService.cancelSubscription(userId);
        }

        return {
          cancelAt: result.cancel_at ? new Date(result.cancel_at * 1000) : null,
          cancelAtPeriodEnd: result.cancel_at_period_end,
          willEndImmediately: !cancelAtPeriodEnd,
        };
      } catch (error) {
        console.error('Error cancelling subscription:', error);
        throw error;
      }
    },

    // Update subscription to a new plan
    async updateSubscription(
      userId: number,
      subscriptionId: string,
      newTierId: number
    ): Promise<{
      subscriptionId: string;
      status: string;
      currentPeriodEnd: Date;
      tierId: number;
    }> {
      try {
        // Verify user owns subscription
        const transactions = (await strapi.entityService.findMany('api::transaction.transaction', {
          filters: {
            stripe_subscription_id: subscriptionId,
            user: { id: userId },
          },
          limit: 1,
        })) as any[];

        if (!transactions || transactions.length === 0) {
          throw new Error('Subscription not found or does not belong to this user');
        }

        // Get new tier details
        const newTier = await this.getTierById(newTierId);
        if (!newTier) {
          throw new Error(`Subscription tier with ID ${newTierId} not found`);
        }

        // Get or create Stripe pricing for new tier
        const { priceId: newPriceId } = await this.getOrCreateStripePricing(newTier);

        const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
          apiVersion: '2025-04-30.basil',
        });

        // Get subscription details to find the current item ID
        const currentSubscription = (await stripe.subscriptions.retrieve(subscriptionId)) as any;
        const subscriptionItemId = currentSubscription.items.data[0]?.id;

        if (!subscriptionItemId) {
          throw new Error('Could not find subscription item ID');
        }

        // Update subscription in Stripe
        const updatedSubscription = (await stripe.subscriptions.update(subscriptionId, {
          items: [
            {
              id: subscriptionItemId,
              price: newPriceId,
            },
          ],
          metadata: {
            tierId: newTier.id.toString(),
          },
          proration_behavior: 'create_prorations',
        })) as any;

        // Update transaction with new tier
        await strapi.entityService.update('api::transaction.transaction', transactions[0].id, {
          data: {
            subscription_tier: newTierId,
            stripe_price_id: newPriceId,
            amount: Number(newTier.price),
          },
        });

        // Update user tracking
        const userTrackingService = strapi.service(
          'api::user-tracking-request.user-tracking-request'
        ) as any;
        await userTrackingService.updateSubscription(userId, newTierId, newTier.request_limit);

        return {
          subscriptionId: updatedSubscription.id,
          status: updatedSubscription.status,
          currentPeriodEnd: new Date(updatedSubscription.current_period_end * 1000),
          tierId: newTierId,
        };
      } catch (error) {
        console.error('Error updating subscription:', error);
        throw error;
      }
    },

    // Get user's active subscription
    async getUserSubscription(userId: number) {
      try {
        // Get user tracking stats
        const userTrackingService = strapi.service(
          'api::user-tracking-request.user-tracking-request'
        ) as any;
        const userStats = await userTrackingService.getUserStats(userId);

        // Find active subscriptions
        const transactions = (await strapi.entityService.findMany('api::transaction.transaction', {
          filters: {
            user: { id: userId },
            payment_status: {
              $in: ['completed'],
            },
            current_period_end: {
              $gt: new Date().toISOString(),
            },
            publishedAt: { $ne: null },
          },
          sort: { createdAt: 'desc' },
          limit: 1,
          populate: {
            subscription_tier: {
              filters: {
                publishedAt: { $ne: null }, // Only get published subscription tiers
              },
            },
          },
        })) as any[];

        const activeSubscription = transactions && transactions.length > 0 ? transactions[0] : null;

        // If there's an active subscription, get more details from Stripe
        let stripeSubscription: any | null = null;
        if (activeSubscription?.stripe_subscription_id) {
          try {
            const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
              apiVersion: '2025-04-30.basil',
            });

            stripeSubscription = (await stripe.subscriptions.retrieve(
              activeSubscription.stripe_subscription_id
            )) as any;
          } catch (err) {
            console.error('Error fetching Stripe subscription:', err);
          }
        }

        return {
          subscription_tier: userStats.subscription_tier,
          request_limit: userStats.request_limit,
          request_count: userStats.request_count,
          remaining: userStats.remaining,
          active_subscription: activeSubscription,
          auto_renew: activeSubscription?.auto_renew || false,
          stripe_subscription_id: activeSubscription?.stripe_subscription_id,
          stripe_details: stripeSubscription
            ? {
                status: stripeSubscription.status,
                current_period_end: stripeSubscription.current_period_end,
                cancel_at_period_end: stripeSubscription.cancel_at_period_end,
                cancel_at: stripeSubscription.cancel_at || undefined,
              }
            : null,
        };
      } catch (error) {
        console.error('Error getting user subscription:', error);
        throw error;
      }
    },

    // Get compare tiers
    async getComparePlansData() {
      try {
        const tiers = await strapi.documents('api::subscription-tier.subscription-tier').findMany({
          filters: {
            stripe_recurring_interval: 'month',
          },
          fields: ['name', 'traffic_share_rate', 'stripe_recurring_interval'],
        });

        console.log('Fetched subscription tiers:', tiers);

        return tiers;
      } catch (error) {
        console.error('Error fetching compare plans:', error);
        throw error;
      }
    },

    // Update user's affiliate reference rates based on a specific subscription tier
    async updateUserAffiliateRefRates(
      userId: number,
      tier: any
    ): Promise<{
      success: boolean;
      affiliatesUpdated: number;
      refRate: number | null;
    }> {
      console.log(`[updateUserAffiliateRefRates] Called for userId ${userId} with tier:`, tier);

      try {
        if (!tier || tier.traffic_share_rate === null || tier.traffic_share_rate === undefined) {
          console.log(
            '[updateUserAffiliateRefRates] No valid traffic_share_rate found in tier:',
            tier
          );
          return {
            success: false,
            affiliatesUpdated: 0,
            refRate: null,
          };
        }

        console.log(
          `[updateUserAffiliateRefRates] Using traffic_share_rate: ${tier.traffic_share_rate}`
        );

        // Find all affiliates owned by this user
        const affiliates: any = await strapi.entityService.findMany('api::affiliate.affiliate', {
          filters: {
            user_submitted: {
              id: userId,
            },
          },
          populate: ['airtable_data', 'user_submitted'],
        });

        console.log(
          `[updateUserAffiliateRefRates] Found ${affiliates?.length || 0} affiliates for userId ${userId}`
        );

        if (!affiliates || affiliates.length === 0) {
          return {
            success: true,
            affiliatesUpdated: 0,
            refRate: Number(tier.traffic_share_rate),
          };
        }

        // Update each affiliate's airtable_data.user_ref_rate
        let updatedCount = 0;
        for (const affiliate of affiliates) {
          console.log(`[updateUserAffiliateRefRates] Updating affiliate ${affiliate.id}`);

          // Prepare the updated airtable_data
          let airtableData = affiliate.airtable_data || {};
          airtableData = {
            ...airtableData,
            user_ref_rate: Number(tier.traffic_share_rate),
          };

          try {
            // First update the content data
            await strapi.documents('api::affiliate.affiliate').update({
              documentId: affiliate.documentId,
              data: {
                airtable_data: airtableData,
                publishedAt: new Date(), // Set as published by providing a publishedAt timestamp
              },
            });

            await strapi.documents('api::affiliate.affiliate').publish({
              documentId: affiliate.documentId,
            });

            // Increment the updated count
            updatedCount++;
            console.log(
              `[updateUserAffiliateRefRates] Updated affiliate ${affiliate.id} with rate ${tier.traffic_share_rate}`
            );
          } catch (err) {
            console.error(`Error updating affiliate ${affiliate.id}:`, err);
          }
        }

        console.log(
          `[updateUserAffiliateRefRates] Successfully updated ${updatedCount} affiliates with rate ${tier.traffic_share_rate}`
        );
        return {
          success: true,
          affiliatesUpdated: updatedCount,
          refRate: Number(tier.traffic_share_rate),
        };
      } catch (error) {
        console.error(
          '[updateUserAffiliateRefRates] Error updating user affiliate reference rates:',
          error
        );
        throw error;
      }
    },
  })
);
