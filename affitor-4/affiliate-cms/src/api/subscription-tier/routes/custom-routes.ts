export default {
  routes: [
    // {
    //   method: 'POST',
    //   path: '/subscription-tiers/subscribe',
    //   handler: 'subscription-tier.subscribe',
    //   config: {
    //     policies: [],
    //     middlewares: [],
    //   },
    // },
    // {
    //   method: 'GET',
    //   path: '/subscription-tiers/current',
    //   handler: 'subscription-tier.getCurrentSubscription',
    //   config: {
    //     policies: [],
    //     middlewares: [],
    //   },
    // },
    {
      method: 'POST',
      path: '/subscription-tiers/create-checkout-session',
      handler: 'subscription-tier.createCheckoutSession',
      config: {
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/subscription-tiers/webhook',
      handler: 'subscription-tier.handleStripeWebhook',
      config: {
        auth: false,
        bodyParser: false, // Disable default body parsing for this route
      },
    },
    {
      method: 'GET',
      path: '/subscription-tiers/compare-plans',
      handler: 'subscription-tier.getComparePlans',
      config: {
        middlewares: [],
      },
    },

    // {
    //   method: 'GET',
    //   path: '/subscription-tiers/verify-subscription',
    //   handler: 'subscription-tier.verifySubscription',
    //   config: {
    //     // policies: ['plugin::users-permissions.isAuthenticated'],
    //     middlewares: [],
    //   },
    // },
  ],
};
