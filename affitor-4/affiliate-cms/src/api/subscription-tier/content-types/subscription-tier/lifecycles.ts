import { errors } from '@strapi/utils';
const { ApplicationError } = errors;

/**
 * Main handler that composes all validation functions
 */
async function beforeSave(event) {
  const { data, where } = event.params;

  console.log({ data });

  // Check if price is being updated
  if (data.price !== undefined) {
    try {
      // Get the current entity to compare prices
      const existingEntity = await strapi.entityService.findOne(
        'api::subscription-tier.subscription-tier',
        where.id
      );

      // If price has changed, remove the stripe_price_id
      if (existingEntity && existingEntity.price !== data.price) {
        data.stripe_price_id = null;
      }
    } catch (error) {
      // If there's an error, continue without modifying stripe_price_id
      console.error('Error checking price change:', error);
    }
  }

  // Update the event params with validated data
  event.params.data = data;
}

export default {
  beforeUpdate: beforeSave,
};
