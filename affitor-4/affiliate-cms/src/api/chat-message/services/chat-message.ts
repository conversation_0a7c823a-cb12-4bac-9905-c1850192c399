/**
 * chat-message service
 *
 * Note: Special AI commands (like /generate) have been migrated to the aiscript system.
 * This service now only handles basic chatbot interactions via the N8N webhook.
 *
 * For AI command processing, see: affiliate-cms/src/api/aiscript/services/aiscript.ts
 */

import { factories } from '@strapi/strapi';
import axios from 'axios';
import { IChatMessage } from '../../chat-session/interfaces';

export default factories.createCoreService('api::chat-message.chat-message', {
  async createMessage(
    content: string,
    sessionId: string,
    direction: 'incoming' | 'outgoing'
  ): Promise<IChatMessage | any> {
    try {
      const message = await strapi.entityService.create('api::chat-message.chat-message', {
        data: {
          content,
          session: sessionId,
          direction,
          timestamp: new Date(),
        },
      });
      return message;
    } catch (error) {
      console.error('Error creating chat message:', error);
      throw error;
    }
  },

  async sendToChatbot(message: string, sessionId: string): Promise<string> {
    try {
      const response = await axios.post(
        'https://tovietthang.app.n8n.cloud/webhook/df4519b4-4d00-4ce0-9851-817bba13d2ad',
        {
          chatInput: message,
          sessionId: sessionId,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Basic YWRtaW46YWRtaW4=',
          },
        }
      );

      // Assuming the chatbot response is in the data property of the response
      return response.data && response.data.length
        ? response.data[0].output
        : 'Sorry, I could not process your request';
    } catch (error) {
      console.error('Error sending message to chatbot:', error);
      return 'Sorry, there was an error connecting to the chatbot service.';
    }
  },
});
