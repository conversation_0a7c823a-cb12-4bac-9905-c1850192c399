export default {
  routes: [
    {
      method: 'GET',
      path: '/auth/google/url',
      handler: 'auth.getGoogleAuthUrl',
      config: {
        auth: false,
        middlewares: [],
      },
    },
    {
      // The path should NOT include the /api prefix as <PERSON><PERSON><PERSON> adds this automatically
      method: 'GET',
      path: '/connect/google/callback',
      handler: 'auth.googleCallback',
      config: {
        auth: false,
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/auth/validate-token',
      handler: 'auth.validateToken',
      config: {
        auth: false,
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/auth/signup',
      handler: 'auth.signup',
      config: {
        auth: false,
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/auth/signin',
      handler: 'auth.signin',
      config: {
        auth: false,
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/auth/verify-email',
      handler: 'auth.verifyEmail',
      config: {
        auth: false,
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/auth/firebase/google/url',
      handler: 'auth.getFirebaseGoogleAuthUrl',
      config: {
        auth: false,
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/auth/firebase/authenticate',
      handler: 'auth.firebaseAuthenticate',
      config: {
        auth: false,
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/auth/firebase/google/callback',
      handler: 'auth.firebaseGoogleCallback',
      config: {
        auth: false,
        middlewares: [],
      },
    },
  ],
};
