/**
 * Adplexity data retrieval using <PERSON><PERSON><PERSON><PERSON> for authentication and direct API calls
 */

import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import path from 'path';
import fs from 'fs';
import https from 'https';

// Types for Adplexity API
interface AdplexitySearchParams {
  mode?: string;
  subMode?: string;
  from?: string;
  to?: string;
  query?: string;
  querySubject?: string;
  order?: string;
  daysRunningFrom?: number;
  daysRunningTo?: number | null;
  alertDomains?: Record<string, unknown>;
  imageTags?: string[];
  bidPriceFrom?: number;
  bidPriceTo?: number | null;
  videoLengthFrom?: number;
  videoLengthTo?: number | null;
  videoLikesFrom?: number;
  videoLikesTo?: number | null;
  videoViewsFrom?: number;
  videoViewsTo?: number | null;
  countriesCountFrom?: number;
  countriesCountTo?: number | null;
  favFolderId?: number | null;
  advancedFilter?: Record<string, unknown>;
  deviceType?: { values: string[]; exclusiveSearch: boolean };
  adType?: { values: string[]; exclusiveSearch: boolean };
  adCategory?: { values: string[]; exclusiveSearch: boolean };
  imageSize?: { values: string[]; exclusiveSearch: boolean };
  country?: { values: string[]; exclusiveSearch: boolean };
  language?: { values: string[]; exclusiveSearch: boolean };
  connection?: { values: string[]; exclusiveSearch: boolean };
  network?: { values: string[]; exclusiveSearch: boolean };
  affNetwork?: { values: string[]; exclusiveSearch: boolean };
  arbitrageNetwork?: { values: string[]; exclusiveSearch: boolean };
  technology?: { values: string[]; exclusiveSearch: boolean };
  tracking?: { values: string[]; exclusiveSearch: boolean };
  videoCategory?: { values: string[]; exclusiveSearch: boolean };
  videoType?: { values: string[]; exclusiveSearch: boolean };
  offset?: number;
  count?: number;
}

interface AdplexityRecord {
  id: string;
  title: string;
  imageUrl: string;
  videoUrl: string;
  country: string;
  duration: string;
  views: string | number;
  likes: string | number;
  firstSeen: string;
  lastSeen: string;
  advertiserName: string;
  advertiserDomain: string;
  landingPage: string;
  fullUrl: string;
}

interface AdplexityResponse {
  total: number;
  records: AdplexityRecord[];
}

// Create a directory for screenshots if it doesn't exist
const screenshotDir = path.join(__dirname, 'screenshots');
if (!fs.existsSync(screenshotDir)) {
  fs.mkdirSync(screenshotDir);
}

/**
 * Helper function to format date in YYYY-MM-DD format
 * @param {Date} date - Date object to format
 * @returns {string} - Formatted date string
 */
function formatDate(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/**
 * Make a direct API request to Adplexity search API
 * @param {Array} cookies - Array of cookie objects from browser
 * @param {Object} searchParams - Search parameters
 * @returns {Promise<AdplexityResponse>} - Promise resolving to search results
 */
function searchAdplexityAPI(
  cookies: any,
  searchParams: AdplexitySearchParams = {}
): Promise<AdplexityResponse> {
  return new Promise((resolve, reject) => {
    // Create cookie string from cookie objects
    const cookieString = cookies.map((c) => `${c.name}=${c.value}`).join('; ');

    // Find XSRF token from cookies
    const xsrfCookie = cookies.find((c) => c.name === 'XSRF-TOKEN');
    let xsrfToken = '';
    if (xsrfCookie) {
      xsrfToken = decodeURIComponent(xsrfCookie.value);
    }

    // Default search parameters based on the curl example
    const defaultParams: AdplexitySearchParams = {
      mode: 'keyword',
      subMode: 'ad',
      from: formatDate(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)), // 30 days ago
      to: formatDate(new Date()),
      query: searchParams.query || 'adcreative', // Use provided query or default to adcreative
      querySubject: 'keyword.ad',
      order: 'newest',
      daysRunningFrom: 1,
      daysRunningTo: null,
      alertDomains: {},
      imageTags: [],
      bidPriceFrom: 0,
      bidPriceTo: null,
      videoLengthFrom: 0,
      videoLengthTo: null,
      videoLikesFrom: 0,
      videoLikesTo: null,
      videoViewsFrom: 0,
      videoViewsTo: null,
      countriesCountFrom: 1,
      countriesCountTo: null,
      favFolderId: null,
      advancedFilter: {},
      deviceType: { values: [], exclusiveSearch: false },
      adType: { values: [], exclusiveSearch: false },
      adCategory: { values: [], exclusiveSearch: false },
      imageSize: { values: [], exclusiveSearch: false },
      country: { values: [], exclusiveSearch: false },
      language: { values: [], exclusiveSearch: false },
      connection: { values: [], exclusiveSearch: false },
      network: { values: [], exclusiveSearch: false },
      affNetwork: { values: [], exclusiveSearch: false },
      arbitrageNetwork: { values: [], exclusiveSearch: false },
      technology: { values: [], exclusiveSearch: false },
      tracking: { values: [], exclusiveSearch: false },
      videoCategory: { values: [], exclusiveSearch: false },
      videoType: { values: [], exclusiveSearch: false },
      offset: 0,
      count: 50,
    };

    // Merge default parameters with provided parameters
    const requestData = { ...defaultParams, ...searchParams };
    const requestBody = JSON.stringify(requestData);

    const options = {
      hostname: 'video.adplexity.com',
      path: '/api/search',
      method: 'POST',
      headers: {
        accept: 'application/json',
        'accept-language': 'en-US,en;q=0.9',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'content-length': Buffer.byteLength(requestBody),
        cookie: cookieString,
        origin: 'https://video.adplexity.com',
        pragma: 'no-cache',
        referer: `https://video.adplexity.com/search/keyword?sm=ad&from=-30d&to=0d&q=${encodeURIComponent(requestData.query as string)}&qs=keyword.ad&order=newest&advf=%7B%7D`,
        'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent':
          'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
        'x-requested-with': 'XMLHttpRequest',
        'x-xsrf-token': xsrfToken,
      },
    };

    console.log(`Making API request to Adplexity search API for keyword: ${requestData.query}...`);

    const req = https.request(options, (res) => {
      let data = '';

      // Log response status
      console.log(`API Response Status Code: ${res.statusCode}`);

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        if (res.statusCode === 200) {
          try {
            const jsonResponse = JSON.parse(data);
            console.log('Response data structure:', Object.keys(jsonResponse));

            // This API response might be in different formats, try to normalize it
            let adRecords: any[] = [];

            // Try to find the ads in common locations
            if (Array.isArray(jsonResponse.ads)) {
              console.log('Found ads array in root');
              adRecords = jsonResponse.ads;
              console.log(`Found ${adRecords.length} ads in root`);
            } else if (Array.isArray(jsonResponse.results)) {
              console.log('Found results array in root');
              adRecords = jsonResponse.results;
            } else if (jsonResponse.data && Array.isArray(jsonResponse.data.ads)) {
              console.log('Found ads array in data property');
              adRecords = jsonResponse.data.ads;
            }

            // If we still don't have the ads, do a recursive search
            if (adRecords.length === 0) {
              console.log('Searching for any array properties that might contain ads...');

              // Function to search through object properties recursively
              const findAdArrays = (obj: any): any[] | null => {
                if (!obj || typeof obj !== 'object') return null;

                // Check direct arrays first
                for (const key of Object.keys(obj)) {
                  if (Array.isArray(obj[key]) && obj[key].length > 0) {
                    const firstItem = obj[key][0];
                    // Check if this looks like an ad by checking for common ad fields
                    if (
                      firstItem &&
                      typeof firstItem === 'object' &&
                      (firstItem.id ||
                        firstItem.title ||
                        firstItem.youtube ||
                        firstItem.image_url ||
                        firstItem.advertiserName)
                    ) {
                      console.log(
                        `Found potential ad data in property: ${key} with ${obj[key].length} items`
                      );
                      return obj[key];
                    }
                  }
                }

                // Recursively check nested objects
                for (const key of Object.keys(obj)) {
                  if (typeof obj[key] === 'object' && obj[key] !== null) {
                    const result = findAdArrays(obj[key]);
                    if (result) return result;
                  }
                }

                return null;
              };

              const foundAds = findAdArrays(jsonResponse);
              if (foundAds) {
                adRecords = foundAds;
              }
            }

            console.log(`Found ${adRecords.length} ad records in API response`);

            // If we still don't have any records, fall back to the original results if they exist
            if (adRecords.length === 0 && jsonResponse.results) {
              console.log('Using original results array as fallback');
              adRecords = jsonResponse.results;
            }

            // Format the response in our standard structure
            const formattedResults: AdplexityResponse = {
              total: jsonResponse.total || adRecords.length,
              records: adRecords.map((item: any) => {
                // Keep the original item intact to preserve all fields
                return item;
              }),
            };

            console.log(
              `Formatted API response contains ${formattedResults.records.length} records`
            );

            // Log the structure of the first record to help with debugging
            if (formattedResults.records.length > 0) {
              console.log('First record structure:', Object.keys(formattedResults.records[0]));
            }

            resolve(formattedResults);
          } catch (error) {
            console.error('Error parsing API response:', error);
            reject(error);
          }
        } else {
          console.error(`API request failed with status code ${res.statusCode}`);
          reject(new Error(`API request failed with status code ${res.statusCode}`));
        }
      });
    });

    req.on('error', (error) => {
      console.error('Error making API request:', error);
      reject(error);
    });

    req.write(requestBody);
    req.end();
  });
}

/**
 * Login to Adplexity and search for video ads using Puppeteer for authentication
 * and direct API calls for data retrieval
 * @returns {Promise<AdplexityResponse>} - Promise resolving to the search results
 */
export async function getAdplexityDataYoutube(
  searchParams: AdplexitySearchParams = {}
): Promise<AdplexityResponse> {
  console.log(
    `Starting browser automation with Puppeteer for authentication to search: ${searchParams.query || 'adcreative'}...`
  );

  // Get username and password from environment variables
  const username = process.env.ADPLEXITY_USERNAME;
  const password = process.env.ADPLEXITY_PASSWORD;

  if (!username || !password) {
    throw new Error('ADPLEXITY_USERNAME and ADPLEXITY_PASSWORD environment variables must be set');
  }

  // Launch a headless browser
  const browser: Browser = await puppeteer.launch({
    headless: true, // Changed from 'shell' to true for compatibility
    args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage', '--disable-gpu'],
    defaultViewport: null, // Use default browser viewport
    executablePath: process.env.PUPPETEER_EXECUTABLE_PATH || undefined, // Use environment variable if provided
  });

  try {
    const page: Page = await browser.newPage();

    // Set viewport dimensions
    await page.setViewport({ width: 1280, height: 800 });

    console.log('Step 1: Navigating to Adplexity login page...');
    await page.goto('https://adplexity.com/members/member', {
      waitUntil: 'networkidle2',
      timeout: 60000, // Increase timeout for initial page load
    });

    console.log('Step 2: Entering login credentials...');
    await page.type('#amember-login', username);
    await page.type('#amember-pass', password);

    console.log('Step 3: Submitting login form...');

    // Click the login button and wait for navigation
    await Promise.all([
      page.click('input[type="submit"]'),
      page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 60000 }),
    ]);

    // Storage for the /api/search response
    let apiSearchResponse: any = null;
    const testquery = searchParams.query;
    console.log('testquery', testquery);

    // Enable request interception
    await page.setRequestInterception(true);

    // Capture network requests/responses to better debug API interactions
    page.on('request', (request) => {
      // Log API requests
      if (
        request.url().includes('/api/search') &&
        !request.url().includes('counters') &&
        !request.url().includes('filters') &&
        request.method() === 'POST'
      ) {
        console.log(`Network request: ${request.method()} ${request.url()}`);
        try {
          const postData = request.postData();
          if (postData) {
            console.log('Search POST data:', postData.substring(0, 200) + '...');
          }
        } catch (e) {
          // Ignore errors reading post data
        }
      }
      request.continue();
    });

    page.on('response', async (response) => {
      // Specifically look for the /api/search response
      if (
        response.url().includes('/api/search') &&
        !response.url().includes('counters') &&
        !response.url().includes('filters') &&
        response.status() === 200
      ) {
        console.log(`Found API search response: ${response.status()} ${response.url()}`);
        try {
          // Store the /api/search response for direct use
          const responseJson = await response.json();
          console.log('API search response found:', Object.keys(responseJson));
          apiSearchResponse = responseJson;
        } catch (e) {
          console.error('Error parsing /api/search response:', e);
        }
      }
    });

    console.log('Step 4: Login successful! Navigating to video.adplexity.com...');

    // Use the provided keyword or default to 'adcreative'
    const searchKeyword = searchParams.query || 'adcreative';

    // Navigate to the video page to get the specialized cookies and trigger search request
    await page.goto(
      `https://video.adplexity.com/search/advertiser?sm=ad&from=-90d&to=0d&q=${encodeURIComponent(searchKeyword)}&qs=advertiser.ad&order=newest`,
      {
        waitUntil: 'networkidle2',
        timeout: 90000,
      }
    );

    // Extract all cookies after successful login and navigation
    const cookies = await page.cookies();
    console.log(`Extracted ${cookies.length} cookies from session`);

    // Check if we captured the /api/search response directly
    if (apiSearchResponse && (apiSearchResponse.ads || apiSearchResponse.results)) {
      console.log('Using captured /api/search response directly');

      // Format the response according to our expected structure
      const results = apiSearchResponse.ads || apiSearchResponse.results || [];
      const formattedResults: AdplexityResponse = {
        total: apiSearchResponse.total || results.length,
        records: results,
      };

      console.log('formattedResults', formattedResults);

      console.log(
        `Directly captured API response contains ${formattedResults.records.length} records`
      );
      return formattedResults;
    }

    // Fallback to our original API call method if we didn't capture the response
    console.log('Falling back to direct API call method');

    // Use the direct API approach with the extracted cookies
    console.log(
      `Step 5: Making direct API call to Adplexity search API for keyword: ${searchKeyword}...`
    );

    // Create today's date and date 30 days ago in YYYY-MM-DD format
    const today = new Date();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(today.getDate() - 30);

    const defaultSearchParams: AdplexitySearchParams = {
      mode: 'keyword',
      subMode: 'ad',
      from: formatDate(thirtyDaysAgo),
      to: formatDate(today),
      query: searchKeyword,
      querySubject: 'keyword.ad',
      order: 'newest',
      daysRunningFrom: 1,
      offset: 0,
      count: 50,
    };

    // Merge the default search params with any provided search params
    const mergedSearchParams = { ...defaultSearchParams, ...searchParams };

    // Call the API function and return results directly
    return await searchAdplexityAPI(cookies, mergedSearchParams);
  } catch (error) {
    console.error('Error during Adplexity data retrieval:', error);
    throw error;
  } finally {
    // Always close the browser to free resources
    await browser.close();
    console.log('Browser closed.');
  }
}

// Export a simplified client interface following the pattern of other API clients
export const AdplexityClient = {
  searchYoutube: async (
    query: string = 'adcreative',
    options: Partial<AdplexitySearchParams> = {}
  ) => {
    try {
      console.log(`AdplexityClient searching YouTube ads for keyword: ${query}`);
      const response = await getAdplexityDataYoutube({
        query,
        ...options,
      });
      console.log(
        `AdplexityClient found ${response.records.length} YouTube ads for keyword: ${query}`
      );
      return response;
    } catch (error) {
      console.error(`AdplexityClient search error for keyword "${query}":`, error);
      throw error;
    }
  },
};
