# ECR Image configuration
ecr_image = "699475955027.dkr.ecr.us-east-1.amazonaws.com/affiliate:latest"

# AWS Region
aws_region = "us-east-1"

# VPC and Network settings
vpc_cidr = "10.1.0.0/16"
public_subnets = ["10.1.1.0/24", "10.1.2.0/24"]
private_subnets = ["10.1.3.0/24", "10.1.4.0/24"]

# ECS settings
ecs_cluster_name = "affiliate-dev-cluster"
ecs_service_name = "affiliate-dev-service"
container_port = 80
desired_count = 1
cpu = 256
memory = 512

# Load balancer settings
lb_name = "affiliate-dev-lb"
lb_internal = false
lb_listener_port = 80
lb_health_check_path = "/health"

# Tags
environment = "development" 