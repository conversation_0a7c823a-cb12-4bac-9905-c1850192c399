import { all, fork } from "redux-saga/effects";
import category<PERSON><PERSON> from "./category/category.saga";
import affiliate<PERSON>aga from "./affiliate/affiliate.saga";
import socialListeningSaga from "./social-listening/social-listening.saga";
import trafficWebSaga from "./traffic-web/traffic-web.saga";
import authSaga from "./auth/auth.saga";
import chatbotSaga from "./chatbot/chatbot.saga";
import aiscriptSaga from "./aiscript/aiscript.saga";
import paymentMethodSaga from "./payment-method/payment-method.saga";
import userSaga from "./user/user.saga";
import subscriptionTierSaga from "./subscription-tier/subscription-tier.saga";
import topVideosSaga from "./top-videos/top-videos.saga";
import topAdsSaga from "./top-ads/top-ads.saga";
import referrerSaga from "./referrer/referrer.saga";
import referrerLinksSaga from "./referrer-links/referrer-links.saga";
import trackLinksSaga from "./track-links/track-links.saga";
import referralActivitySaga from "./referral-activity/referral-activity.saga";
import referralCommissionSaga from "./referral-commission/referral-commission.saga";
import referralSaga from "./referral/referral.saga";
import payoutSaga from "./payout/payout.saga";
import adminSaga from "./admin/admin.saga";
import spyheroSaga from "./spyhero/spyhero.saga";
import { transactionSaga } from "./transaction/transaction.saga";
import discourseSaga from "./discourse/discourse.saga";
import { pageSaga } from "./page/page.saga";

export default function* rootSaga() {
  yield all([
    fork(categorySaga),
    fork(affiliateSaga),
    fork(socialListeningSaga),
    fork(trafficWebSaga),
    fork(authSaga),
    fork(chatbotSaga),
    fork(aiscriptSaga),
    fork(paymentMethodSaga),
    fork(userSaga),
    fork(subscriptionTierSaga),
    fork(topVideosSaga),
    fork(topAdsSaga),
    fork(referrerSaga),
    fork(referrerLinksSaga),
    fork(trackLinksSaga),
    fork(referralActivitySaga),
    fork(referralCommissionSaga),
    fork(referralSaga),
    fork(payoutSaga),
    fork(adminSaga),
    fork(spyheroSaga),
    fork(transactionSaga),
    fork(discourseSaga),
    fork(pageSaga),
  ]);
}
