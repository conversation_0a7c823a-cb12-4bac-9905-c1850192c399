import { call, put, takeEvery, select } from "redux-saga/effects";
import { actions } from "./top-ads.slice";
import { PayloadAction } from "@reduxjs/toolkit";
import { IPagination, ISort } from "@/interfaces";
import qs from "qs";
import { RootState } from "@/store";
import { handleApiError } from "@/utils/error-handler";
import { actions as aiscriptActions } from "@/features/aiscript/aiscript.slice";

function* handleFetch(
  action: PayloadAction<{
    platforms: string[];
    pagination: IPagination;
    sort: ISort[];
    dateFilter?: any;
    searchTerm?: string;
  }>
): Generator<any, void, any> {
  try {
    yield put(actions.setLoading(true));

    // Get current state for filters and sort
    const state: RootState = yield select();
    const { sort, filters, searchTerm } = state.topAds;

    const { pagination, dateFilter = {} } = action.payload;

    // Smart platform selection with fallback chain
    const selectedPlatforms =
      filters.platforms.length > 0
        ? filters.platforms
        : action.payload.platforms.length > 0
        ? action.payload.platforms
        : ["youtube", "tiktok"];

    // Build filters more efficiently
    const buildQueryFilters = () => {
      const queryFilters: any = {
        platform: { $in: selectedPlatforms },
      };

      // Apply date filter if available
      if (dateFilter && Object.keys(dateFilter).length > 0) {
        queryFilters.createdAt = dateFilter;
      }

      // Apply category filter if available
      if (filters.categories.length > 0) {
        if (!queryFilters.affiliate) {
          queryFilters.affiliate = {};
        }
        queryFilters.affiliate.categories = { $in: filters.categories };
      }

      // Apply views filter if available
      if (filters.minViews) {
        const views = parseInt(filters.minViews);
        if (!isNaN(views)) {
          queryFilters.views = { $gte: views };
        }
      }

      // Apply search term filter with improved logic
      const searchTermToUse = action.payload.searchTerm || searchTerm;
      if (searchTermToUse && searchTermToUse.trim()) {
        const trimmedSearch = searchTermToUse.trim();
        console.log(`Applying search filter: "${trimmedSearch}"`);
        queryFilters.$or = [
          { affiliate: { name: { $containsi: trimmedSearch } } },
          {
            affiliate: { categories: { name: { $containsi: trimmedSearch } } },
          },
        ];
      }

      return queryFilters;
    };

    // Generate filters
    const queryFilters = buildQueryFilters();

    const populateConfig = {
      affiliate: {
        populate: {
          image: { fields: ["id", "url"] },
          categories: { fields: ["id", "name", "slug"] },
        },
        fields: ["id", "name", "url", "slug"],
      },
    };

    // Build sort params with improved null handling
    const sortParams = sort?.field
      ? [`${sort.field}:${sort.order || "desc"}`]
      : action.payload.sort?.length > 0
      ? [
          `${action.payload.sort[0].field}:${
            action.payload.sort[0].order || "desc"
          }`,
        ]
      : ["createdAt:desc"]; // Default sort

    const query = qs.stringify(
      {
        filters: queryFilters,
        pagination: {
          page: pagination.page || 1,
          pageSize: pagination.pageSize || 10,
        },
        sort: sortParams,
        populate: populateConfig,
      },
      {
        encodeValuesOnly: true,
      }
    );

    console.log(
      `Fetching ads with: platforms=${selectedPlatforms.join(",")}, page=${
        pagination.page
      }, filters=${Object.keys(queryFilters).length}`
    );

    // Simplified - no manual token/headers handling
    const response: any = yield call(fetch, `/api/top-ads?${query}`, {
      method: "GET",
    });

    if (!response.ok) {
      // Use the centralized error handler
      if (yield call(handleApiError, response)) {
        // set is loading fail
        yield put(actions.setLoading(false));
        return; // Error was handled
      }

      yield put(
        actions.setError(`Request failed with status ${response.status}`)
      );
      return;
    }

    const jsonResponse = yield response.json();
    const { data, meta } = jsonResponse;

    if (!data || !Array.isArray(data)) {
      console.error("Invalid response data structure:", jsonResponse);
      yield put(
        actions.setError("Invalid response: missing or malformed data")
      );
      return;
    }

    if (!meta || !meta.pagination) {
      console.warn("Response missing pagination metadata");
    }

    yield put(actions.setTopAds(data));
    if (meta?.pagination) {
      yield put(actions.setPagination(meta.pagination));
    }

    // Clear any previous errors
    yield put(actions.setError(null));

    console.log(`Loaded ${data.length} ads successfully`);
  } catch (error: any) {
    console.error("Error fetching top ads:", error);
    const errorMessage = error?.message || "Failed to fetch top ads";
    yield put(actions.setError(errorMessage));
  } finally {
    yield put(actions.setLoading(false));
  }
}

// Add a new handler for transcript fetching
function* handleFetchAdTranscript(
  action: PayloadAction<string>
): Generator<any, void, any> {
  try {
    yield put(actions.setLoadingTranscript(true));
    const adId = action.payload;

    // Get auth token from localStorage
    const token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;
    if (!token) {
      yield put(actions.setError("Authentication required"));
      return;
    }

    const response: any = yield call(fetch, `/api/top-ads/transcript/${adId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      // Use the centralized error handler
      if (yield call(handleApiError, response)) {
        yield put(actions.setLoadingTranscript(false));
        return; // Error was handled
      }

      yield put(
        actions.setError(`Failed to fetch transcript: ${response.status}`)
      );
      yield put(actions.setLoadingTranscript(false));
      return;
    }

    const data = yield response.json();
    const { transcript, suggestions, sessionId } = data;

    if (!transcript) {
      yield put(
        actions.setError("No transcript available for this ad content")
      );
      yield put(actions.setLoadingTranscript(false));
      return;
    }

    if (sessionId) {
      yield put(aiscriptActions.setSessionId(sessionId));
    }

    yield put(aiscriptActions.clearMessages());
    yield put(actions.setAdTranscript(`Original script:\n` + transcript));

    // Add the transcript to the AIScript as an AI message with copyable flag
    yield put(
      aiscriptActions.setMessage({
        type: "ai",
        content: `Original script:\n` + transcript,
        copyable: true, // Mark this message as copyable
      })
    );

    // Open the AIScript UI
    yield put(aiscriptActions.openAIScript());

    if (suggestions && suggestions.length > 0) {
      yield put(
        aiscriptActions.setMessage({
          type: "ai",
          content: "How would you like me to optimize your ad transcript?",
          quickReplies: suggestions.map((suggestion: any) => ({
            label: suggestion.title,
            content: sessionId
              ? `Question: ${suggestion.content}`
              : `Context: ${transcript}\n
          Question: ${suggestion.content}
          `,
            promptId: suggestion.id,
          })),
        })
      );
    }

    yield put(actions.setLoadingTranscript(false));
  } catch (error: any) {
    console.log("Ad transcript error", error);
    yield put(actions.setLoadingTranscript(false));
  }
}

export default function* topAdsSaga() {
  yield takeEvery(actions.fetchTopAds.type, handleFetch);
  yield takeEvery(actions.fetchAdTranscript.type, handleFetchAdTranscript);
}
