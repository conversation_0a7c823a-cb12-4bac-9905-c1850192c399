import { IPaymentMethodDetail, IPagination } from "@/interfaces";
import { RootState } from "@/store";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { createSelector } from "reselect";

interface PaymentMethodState {
  list: IPaymentMethodDetail[] | null;
  loading: boolean;
  error: string | null;
  pagination?: IPagination;
}

const initialState: PaymentMethodState = {
  list: null,
  loading: false,
  error: null,
};

const paymentMethodSlice = createSlice({
  name: "paymentMethod",
  initialState,
  reducers: {
    // Trigger action for Saga
    fetchPaymentMethods: (state) => {
      state.loading = true;
      state.error = null;
    },
    
    // Sync actions
    setPaymentMethods: (state, action: PayloadAction<IPaymentMethodDetail[] | null>) => {
      state.list = action.payload;
      state.loading = false;
    },
    
    setPagination: (state, action: PayloadAction<IPagination>) => {
      state.pagination = action.payload;
    },
    
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
      state.loading = false;
    },
  },
});

export const { actions, reducer } = paymentMethodSlice;

// Selectors
const selectPaymentMethodState = (state: RootState) => state.paymentMethod;

export const selectPaymentMethods = createSelector(
  [selectPaymentMethodState],
  (paymentMethodState) => paymentMethodState.list
);

export const selectPaymentMethodsLoading = createSelector(
  [selectPaymentMethodState],
  (paymentMethodState) => paymentMethodState.loading
);

export const selectPaymentMethodsError = createSelector(
  [selectPaymentMethodState],
  (paymentMethodState) => paymentMethodState.error
);

export const selectPaymentMethodsPagination = createSelector(
  [selectPaymentMethodState],
  (paymentMethodState) => paymentMethodState.pagination
);
