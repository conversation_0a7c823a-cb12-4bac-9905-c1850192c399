import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "@/store";
import { createSelector } from "reselect";

export interface ISubscriptionTier {
  id: number;
  documentId: string;
  name: string;
  display_name: string;
  price: number;
  request_limit: number;
  duration_days: number;
  features: any | null;
  is_popular: boolean;
  description: string | null;
  createdAt: string;
  updatedAt: string;
  publishedAt: string | null;
  locale: string | null;
}

export interface IUserTrackingRequest {
  id: number;
  documentId: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string | null;
  locale: string | null;
  subscription_tier: ISubscriptionTier | null;
  request_count: number;
  request_limit: number;
  last_request_date: string;
  current_period_end: string | null;
  transaction: any;
}

export interface IUser {
  id: number;
  documentId: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  username: string;
  email: string;
  provider: string | null;
  confirmed: boolean;
  blocked: boolean;
  subscription_tier: ISubscriptionTier | null;
  user_tracking_request: IUserTrackingRequest | null;
  isReferrer: boolean;
  first_name: string | null;
  last_name: string | null;
  // Additional profile fields for address
  address: string | null;
  apt?: string | null;
  city: string | null;
  state: string | null;
  country: string | null;
  zip_code: string | null;
  // Payment fields
  paypal_email?: string | null;
  bank_transfer?: {
    account_number?: string;
    swift_code?: string;
    first_name?: string;
    last_name?: string;
    business_name?: string;
    country?: string;
    city?: string;
    state?: string;
    address?: string;
    zip_code?: string;
  } | null;
  isPremium?: boolean;
  // New fields for admin customer management
  referrer?: any;
  referrals?: any[];
  totalReferralRevenue?: number;
  totalReferralCommission?: number;
  totalReferrals?: number;
}

// Define profile update payload type
export interface UpdateProfilePayload {
  first_name?: string;
  last_name?: string;
  address?: string;
  apt?: string;
  city?: string;
  state?: string;
  country?: string;
  zip_code?: string;
  paypal_email?: string;
  referrer_code?: string;
  bank_transfer?: {
    account_number?: string;
    swift_code?: string;
    first_name?: string;
    last_name?: string;
    business_name?: string;
    country?: string;
    city?: string;
    state?: string;
    address?: string;
    zip_code?: string;
  };
}

interface UserState {
  data: IUser | null;
  loading: boolean;
  error: string | null;
  isPremium: boolean;
  isUpdating: boolean; // New state for tracking profile updates
  // Add admin users management state
  adminUsers: IUser[];
  adminUsersLoading: boolean;
  adminUsersError: string | null;
  adminUsersPagination: {
    page: number;
    pageSize: number;
    pageCount: number;
    total: number;
  } | null;
}

const initialState: UserState = {
  data: null,
  loading: false,
  error: null,
  isPremium: false,
  isUpdating: false,
  adminUsers: [],
  adminUsersLoading: false,
  adminUsersError: null,
  adminUsersPagination: null,
};

const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    // Async action triggers
    fetchUserMe: (state) => {
      state.loading = true;
      state.error = null;
    },

    // New action for updating user profile
    updateUserProfile: (state, action: PayloadAction<UpdateProfilePayload>) => {
      state.isUpdating = true;
      state.error = null;
    },

    updateUserProfileSuccess: (state) => {
      state.isUpdating = false;
    },

    updateUserProfileFailure: (state, action: PayloadAction<string>) => {
      state.isUpdating = false;
      state.error = action.payload;
    },

    // Admin users management actions
    fetchAdminUsers: (
      state,
      action: PayloadAction<{
        page?: number;
        pageSize?: number;
        search?: string;
        status?: string;
      }>
    ) => {
      state.adminUsersLoading = true;
      state.adminUsersError = null;
    },

    fetchAdminUsersSuccess: (
      state,
      action: PayloadAction<{
        data: IUser[];
        meta: { pagination: { page: number; pageSize: number; pageCount: number; total: number } };
      }>
    ) => {
      state.adminUsersLoading = false;
      state.adminUsers = action.payload.data;
      state.adminUsersPagination = action.payload.meta.pagination;
      state.adminUsersError = null;
    },

    fetchAdminUsersFailure: (state, action: PayloadAction<string>) => {
      state.adminUsersLoading = false;
      state.adminUsersError = action.payload;
    },

    // Sync reducers
    setUserData: (
      state,
      action: PayloadAction<
        IUser & { isPremium?: boolean; isReferrer?: boolean }
      >
    ) => {
      state.data = action.payload;
      state.loading = false;
      state.error = null;

      state.isPremium = action.payload.isPremium || false;
    },

    setUserError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.loading = false;
    },

    clearUserData: (state) => {
      state.data = null;
      state.error = null;
      state.loading = false;
      state.isPremium = false;
    },
  },
});

export const { actions, reducer } = userSlice;

// Selectors
const selectUserState = (state: RootState) => state.user;

export const selectUserIsPremium = createSelector(
  [selectUserState],
  (userState) => userState.isPremium
);

export const selectUserData = createSelector(
  [selectUserState],
  (userState) => userState.data
);

export const selectUserLoading = createSelector(
  [selectUserState],
  (userState) => userState.loading
);

export const selectUserError = createSelector(
  [selectUserState],
  (userState) => userState.error
);

export const selectUserIsUpdating = createSelector(
  [selectUserState],
  (userState) => userState.isUpdating
);

// Add new selectors for admin users
export const selectAdminUsers = createSelector(
  [selectUserState],
  (userState) => userState.adminUsers
);

export const selectAdminUsersLoading = createSelector(
  [selectUserState],
  (userState) => userState.adminUsersLoading
);

export const selectAdminUsersError = createSelector(
  [selectUserState],
  (userState) => userState.adminUsersError
);

export const selectAdminUsersPagination = createSelector(
  [selectUserState],
  (userState) => userState.adminUsersPagination
);
