import { NextApiRequest, NextApiResponse } from "next";
import { StrapiAdminClient } from "@/utils/request";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    // Get admin token from headers
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return res.status(401).json({ error: "Admin authentication required" });
    }

    const token = authHeader.replace("Bearer ", "");
    const { method, query } = req;

    if (method === "GET") {
      const { page = 1, pageSize = 10, search = "", status, sort } = query;

      const data = await StrapiAdminClient.getPartners(
        {
          page: Number(page),
          pageSize: Number(pageSize),
          search: search.toString(),
          status: status ? status.toString() : undefined,
          sort: sort ? sort.toString() : undefined,
        },
        token
      );

      res.status(200).json(data);
    } else if (method === "PUT") {
      // Update partner status
      const { partnerId } = query;
      const { status } = req.body;

      if (!partnerId || !status) {
        return res.status(400).json({
          error: "Partner ID and status are required",
        });
      }

      const data = await StrapiAdminClient.updatePartnerStatus(
        partnerId.toString(),
        status,
        token
      );

      res.status(200).json(data);
    } else if (method === "DELETE") {
      // Delete partner
      const { partnerId } = query;

      if (!partnerId) {
        return res.status(400).json({ error: "Partner ID is required" });
      }

      await StrapiAdminClient.deletePartner(partnerId.toString(), token);

      res.status(200).json({ success: true });
    } else {
      res.status(405).json({ error: "Method not allowed" });
    }
  } catch (error: any) {
    console.error("Admin partners API error:", error);

    // Handle different error types
    if (error.statusCode) {
      return res.status(error.statusCode).json({
        error: error.message || "Operation failed",
      });
    }

    res.status(500).json({ error: "Internal server error" });
  }
}
