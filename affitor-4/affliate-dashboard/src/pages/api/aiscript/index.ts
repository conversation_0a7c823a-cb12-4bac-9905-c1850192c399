import type { NextApiRequest, NextApiResponse } from 'next';
import { StrapiClient } from '@/utils/request';
import { sendApiError } from '@/utils/api-error-handler';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({ statusCode: 405, message: 'Method not allowed' });
  }

  try {
    // Get authorization token from request headers
    const authToken = req.headers.authorization?.split(' ')[1];
    
    if (!authToken) {
      return res.status(401).json({ statusCode: 401, message: 'Unauthorized - Missing token' });
    }

    // Extract message and promptId from request body
    const { message, promptId, sessionId } = req.body;
    
    if (!message) {
      return res.status(400).json({ statusCode: 400, message: 'Bad request - Missing message' });
    }

    try {
      // Use StrapiClient to forward the request to backend with promptId if available
      const response = await StrapiClient.sendScriptMessage(
        message, 
        authToken, 
        promptId, // Pass the promptId to the client method
        sessionId
      );

      console.log("AIScript API response:", response);
      
      // Return the response from the backend
      return res.status(200).json(response);
    } catch (apiError: any) {
      console.error('API Client error:', apiError);
      return sendApiError(res, apiError, "Failed to process script message");
    }
  } catch (error: any) {
    console.error('AIScript API error:', error);
    return sendApiError(
      res,
      error,
      "An error occurred while processing your request"
    );
  }
}
