import { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";
import { createApiContext } from "@/utils/api-middleware";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    const { videoId } = req.query;

    if (!videoId || typeof videoId !== "string") {
      return res.status(400).json({
        success: false,
        error: "Video ID is required",
      });
    }

    // Use centralized context
    const { token } = createApiContext(req);

    // Call the Strapi backend to get the transcript
    const response: any = await StrapiClient.client.get(
      `/api/social-listenings/custom-transcript/${videoId}`,
      {
        headers: token ? { Authorization: `Bearer ${token}` } : {},
      }
    );

    // Check if the response has the expected structure
    if (!response || typeof response !== "object") {
      throw new Error("Invalid response from transcript service");
    }

    // Return the transcript data
    return res.status(200).json({
      success: true,
      videoId,
      transcript: response.transcript || "",
      source: response.source || "api",
    });
  } catch (error: any) {
    console.error("Error fetching YouTube transcript:", error);
    return res.status(500).json({
      success: false,
      error: error.message || "Failed to fetch transcript",
    });
  }
}
