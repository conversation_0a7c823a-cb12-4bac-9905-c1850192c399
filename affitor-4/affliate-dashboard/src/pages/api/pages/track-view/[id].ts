import { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    const { id } = req.query;
    const { referrer, source_type = 'direct' } = req.body;

    if (!id || typeof id !== 'string') {
      return res.status(400).json({ error: "Page ID is required" });
    }

    const { token } = createApiContext(req, { requireAuth: false });

    // Forward the request to Strapi backend
    const response = await StrapiClient.trackPageView(id, {
      referrer,
      source_type
    }, token);

    return res.status(200).json(response);
  } catch (error: any) {
    console.error("Error tracking page view:", error);
    sendApiError(res, error, "Error tracking page view");
  }
}
