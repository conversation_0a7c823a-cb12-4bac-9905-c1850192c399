import { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { method } = req;
  const { id } = req.query;

  console.log('📄 [Page API] Incoming request:', {
    method,
    id,
    idType: typeof id,
    query: req.query,
    url: req.url,
  });

  if (!id || typeof id !== 'string') {
    console.log('📄 [Page API] Invalid page ID:', { id, type: typeof id });
    return res.status(400).json({
      statusCode: 400,
      message: "Page ID is required"
    });
  }

  console.log('📄 [Page API] Valid page ID received:', id);

  try {
    switch (method) {
      case "GET":
        // GET requests allow public access for page viewing
        const { token } = createApiContext(req, { requireAuth: false });
        return handleGet(req, res, token, id);
      case "PUT":
        // PUT requests require authentication for editing
        const { token: putToken } = createApiContext(req, { requireAuth: true });
        return handlePut(req, res, putToken!, id);
      case "DELETE":
        // DELETE requests require authentication
        const { token: deleteToken } = createApiContext(req, { requireAuth: true });
        return handleDelete(req, res, deleteToken!, id);
      default:
        return res.status(405).json({ error: "Method not allowed" });
    }
  } catch (error: any) {
    console.error("Page API error:", error);
    return sendApiError(res, error, "An error occurred while processing your request");
  }
}

async function handleGet(req: NextApiRequest, res: NextApiResponse, token: string | undefined, id: string) {
  try {
    console.log('📄 [Page API GET] Starting page fetch for ID:', id);
    console.log('📄 [Page API GET] Request details:', {
      pageId: id,
      hasToken: !!token,
      tokenPrefix: token ? token.substring(0, 10) + '...' : 'No token',
      endpoint: `/api/pages/${id}`,
      isPublicAccess: !token,
    });

    // Call Strapi API to get specific page
    console.log('📄 [Page API GET] Making request to Strapi:', `/api/pages/${id}`);

    // Prepare headers - include auth if available
    const headers: any = {};
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const response: any = await StrapiClient.client.get(`/api/pages/${id}?populate=author`, {
      headers
    });

    console.log('📄 [Page API GET] Strapi response received');
    console.log('📄 [Page API GET] Response status:', response?.status);
    console.log('📄 [Page API GET] Response structure analysis:', {
      hasResponse: !!response,
      hasData: !!response?.data,
      dataType: typeof response?.data,
      isDataArray: Array.isArray(response?.data),
      hasDataProperty: !!response?.data?.data,
      dataPropertyType: typeof response?.data?.data,
      responseKeys: response?.data ? Object.keys(response.data) : [],
    });

    if (response?.data?.data) {
      console.log('📄 [Page API GET] Found data.data property - Standard Strapi format');
      console.log('📄 [Page API GET] Page data:', {
        id: response.data.data.id,
        documentId: response.data.data.documentId,
        title: response.data.data.title,
        status: response.data.data.status,
        hasContent: !!response.data.data.content,
        contentType: typeof response.data.data.content,
      });
    } else if (response?.data) {
      console.log('📄 [Page API GET] Direct data format');
      console.log('📄 [Page API GET] Page data:', {
        id: response.data.id,
        documentId: response.data.documentId,
        title: response.data.title,
        status: response.data.status,
        hasContent: !!response.data.content,
        contentType: typeof response.data.content,
        contentValue: response.data.content,
        contentKeys: response.data.content ? Object.keys(response.data.content) : 'N/A',
      });
    } else {
      console.log('📄 [Page API GET] Unexpected response format');
      console.log('📄 [Page API GET] Raw response:', response);
    }

    console.log('📄 [Page API GET] Returning response to frontend');
    return res.status(200).json(response.data);
  } catch (error: any) {
    console.error("📄 [Page API GET] Error fetching page:", error);
    console.error("📄 [Page API GET] Error details:", {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      pageId: id,
    });
    return sendApiError(res, error, "Failed to fetch page");
  }
}

async function handlePut(req: NextApiRequest, res: NextApiResponse, token: string, id: string) {
  try {
    const { data } = req.body;

    if (!data) {
      return res.status(400).json({ 
        statusCode: 400, 
        message: "Page data is required" 
      });
    }

    // Call Strapi API to update page
    const response = await StrapiClient.client.put(`/api/pages/${id}?populate=author`, {
      data,
    }, {
      headers: {
        Authorization: `Bearer ${token}`,
      }
    });

    console.log('📄 [Page API PUT] Update response received:', {
      hasResponse: !!response,
      hasData: !!response?.data,
      hasNestedData: !!response?.data?.data,
      responseKeys: response?.data ? Object.keys(response.data) : [],
      nestedDataKeys: response?.data?.data ? Object.keys(response.data.data) : [],
      hasAuthor: response?.data?.data?.author || response?.data?.author,
      authorStructure: response?.data?.data?.author || response?.data?.author
    });

    return res.status(200).json(response.data);
  } catch (error: any) {
    console.error("Error updating page:", error);
    return sendApiError(res, error, "Failed to update page");
  }
}

async function handleDelete(req: NextApiRequest, res: NextApiResponse, token: string, id: string) {
  try {
    // Call Strapi API to delete page
    const response = await StrapiClient.client.delete(`/api/pages/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    return res.status(200).json(response.data);
  } catch (error: any) {
    console.error("Error deleting page:", error);
    return sendApiError(res, error, "Failed to delete page");
  }
}
