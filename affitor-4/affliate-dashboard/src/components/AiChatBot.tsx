"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { actions } from "@/features/chatbot/chatbot.slice";
import {
  selectChatbotMessages,
  selectChatbotLoading,
  selectIsChatbotOpen,
  selectChatbotQuickReplies,
  QuickReply,
} from "@/features/chatbot/chatbot.slice";
import { selectIsAuthenticated } from "@/features/auth/auth.slice";

export default function AiChatBot() {
  const router = useRouter();
  const dispatch = useDispatch();
  const messages = useSelector(selectChatbotMessages);
  const isLoading = useSelector(selectChatbotLoading);
  const isOpen = useSelector(selectIsChatbotOpen); // Use global state instead of local state
  const [inputMessage, setInputMessage] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const isAuth = useSelector(selectIsAuthenticated);
  const quickReplies = useSelector(selectChatbotQuickReplies); // Use quick replies from Redux
  // Track expanded messages by their index
  const [expandedMessages, setExpandedMessages] = useState<Set<number>>(new Set());
  // Track expanded mode for the chatbox
  const [isExpanded, setIsExpanded] = useState(false);
  const [copiedMessageIndex, setCopiedMessageIndex] = useState<number | null>(null);

  // Character limit for message truncation
  const CHARACTER_LIMIT = 255;

  // Scroll to bottom when messages update
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, isOpen]);

  const handleSendMessage = () => {
    if (!inputMessage.trim() || isLoading) return;

    // Dispatch action to send message
    console.log("inputMessage", inputMessage);
    dispatch(actions.sendMessage(inputMessage));
    setInputMessage("");
  };

  // Update handleQuickReply to only handle QuickReply objects
  const handleQuickReply = (reply: QuickReply) => {
    if (isLoading) return;
    
    // Display the label in the UI but send the full content to the backend
    dispatch(actions.sendMessage(reply.content));
    
    // For display purposes only, show just the label in the UI
    const userMessageElem = document.querySelector('.ai-chatbot-message:last-child');
    if (userMessageElem) {
      userMessageElem.textContent = reply.label;
    }
  };

  const handleCloseChat = () => {
    // End the session if user is authenticated
    if (isAuth) {
      dispatch(actions.endSession());
    } else {
      // Just close the chatbot if not authenticated
      dispatch(actions.closeChatBot());
    }
  };

  // Toggle chatbot when button is clicked
  const toggleChatbot = () => {
    dispatch(actions.toggleChatBot());
  };

  // Check if a message is expanded
  const isMessageExpanded = (index: number) => expandedMessages.has(index);

  // Toggle message expansion
  const toggleMessageExpansion = (index: number) => {
    const newExpandedMessages = new Set(expandedMessages);
    if (newExpandedMessages.has(index)) {
      newExpandedMessages.delete(index);
    } else {
      newExpandedMessages.add(index);
    }
    setExpandedMessages(newExpandedMessages);

    // Give a small delay before scrolling to ensure the DOM has updated
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }, 50);
  };

  // Format message content with inline "Read more" option if needed
  const formatMessageContent = (content: string, index: number) => {
    const isLongMessage = content.length > CHARACTER_LIMIT;
    
    // Create a formatted content element with HTML support
    const createContentElement = (text: string) => {
      return (
        <span dangerouslySetInnerHTML={{ 
          __html: text.replace(
            'Original script:', 
            '<div class="font-bold w-full">Original script:</div>'
          ) 
        }} />
      );
    };

    if (!isLongMessage) {
      return createContentElement(content);
    }

    if (isMessageExpanded(index)) {
      return (
        <>
          {createContentElement(content)}{" "}
          <button
            onClick={(e) => {
              e.stopPropagation();
              toggleMessageExpansion(index);
            }}
            className="text-xs text-blue-500 hover:underline inline-block ml-1"
          >
            Show less
          </button>
        </>
      );
    }

    return (
      <>
        {createContentElement(content.substring(0, CHARACTER_LIMIT))}<span>...</span>{" "}
        <button
          onClick={(e) => {
            e.stopPropagation();
            toggleMessageExpansion(index);
          }}
          className="text-xs text-blue-500 hover:underline inline-block ml-1"
        >
          Read more
        </button>
      </>
    );
  };

  // Handle expand/collapse for the chatbox
  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
    // Give a small delay before scrolling to ensure the DOM has updated after expansion
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }, 50);
  };

  // Function to copy message content to clipboard
  const handleCopyMessage = (content: string, index: number) => {
    navigator.clipboard.writeText(content).then(
      () => {
        // Set the index of the copied message to show feedback
        setCopiedMessageIndex(index);

        // Reset the copied state after 2 seconds
        setTimeout(() => {
          setCopiedMessageIndex(null);
        }, 2000);
      },
      (err) => {
        console.error("Could not copy text: ", err);
      }
    );
  };

  return (
    <div className="fixed bottom-4 right-4 z-40 bg-transparent">
      <button
        onClick={toggleChatbot}
        className="w-12 h-12 rounded-full bg-primary text-primary-foreground flex items-center justify-center shadow-lg"
      >
        <i className="fas fa-robot"></i>
      </button>

      {isOpen && (
        <div
          className={`absolute md:min-h-[600px]  min-h-[70vh] ${
            isExpanded
              ? "bottom-0 right-0 w-[80vw] h-[80vh]"
              : "bottom-16 right-0 w-96 max-w-[80vw] max-h-[60vh]"
          } shadow-lg border bg-white transition-all duration-300 rounded-lg flex flex-col overflow-hidden`}
        >
          <div className="flex items-center justify-between p-4 border-b bg-blue-800 text-white">
            <div className="font-semibold">AffitorAI Assistant</div>
            <div className="flex gap-2">
              <button
                onClick={toggleExpand}
                className="text-white hover:text-gray-200"
                title={isExpanded ? "Collapse" : "Expand"}
              >
                <i
                  className={`fas ${isExpanded ? "fa-compress" : "fa-expand"}`}
                ></i>
              </button>
              <button
                onClick={() => dispatch(actions.closeChatBot())}
                className="text-white hover:text-gray-200"
              >
                <i className="fas fa-minus"></i>
              </button>
              <button
                onClick={handleCloseChat}
                className="text-white hover:text-gray-200"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-blue-300">
            {messages.map((message: any, index: number) => (
              <div
                key={index}
                className={`flex flex-col ${
                  message.type === "user" ? "items-end" : "items-start"
                }`}
              >
                <div
                  className={`flex ${
                    message.type === "user" ? "justify-end" : "justify-start"
                  } w-full`}
                >
                  <div
                    className={`flex gap-2 max-w-[80%] ${
                      message.type === "user" ? "flex-row-reverse" : ""
                    }`}
                  >
                    {message.type === "ai" && (
                      <div className="w-8 h-8 rounded-full bg-blue-800 text-white flex items-center justify-center flex-shrink-0">
                        <i className="fas fa-robot"></i>
                      </div>
                    )}
                    {message.type === "user" && (
                      <div className="w-8 h-8 rounded-full bg-blue-800 text-white flex items-center justify-center flex-shrink-0">
                        <i className="fas fa-user"></i>
                      </div>
                    )}
                    <div
                      className={`${
                        message.type === "user"
                          ? "bg-blue-800 text-white"
                          : "bg-white text-gray-800"
                      } rounded-lg p-3 shadow relative`}
                    >
                      {/* Copy button for AI messages that are marked as copyable */}
                      {message.type === "ai" && message.copyable && (
                        <button
                          onClick={() =>
                            handleCopyMessage(message.content, index)
                          }
                          className="absolute top-1 right-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-600 rounded px-1.5 py-0.5 opacity-70 hover:opacity-100 transition-opacity mt-0.5 mr-0.5 z-10"
                          title="Copy transcript"
                          style={{ boxShadow: '0 0 3px rgba(0,0,0,0.1)' }}
                        >
                          {copiedMessageIndex === index ? (
                            <span className="flex items-center gap-1">
                              <i className="fas fa-check text-green-500"></i>
                              <span>Copied</span>
                            </span>
                          ) : (
                            <span className="flex items-center gap-1">
                              <i className="fas fa-copy"></i>
                              <span>Copy</span>
                            </span>
                          )}
                        </button>
                      )}
                      {/* Remove the pr-16 padding and let text flow normally */}
                      <div>
                        {formatMessageContent(message.content, index)}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Message-specific quick replies */}
                {message.type === "ai" &&
                  message.quickReplies &&
                  message.quickReplies.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-2 ml-10 mr-10">
                      {message.quickReplies.map(
                        (reply: QuickReply, replyIdx: number) => (
                          <button
                            key={replyIdx}
                            onClick={() => handleQuickReply(reply)}
                            className="px-3 py-1 rounded-full bg-white hover:bg-blue-50 text-blue-800 text-xs border border-blue-400 shadow-sm transition-colors"
                          >
                            {reply.label}
                          </button>
                        )
                      )}
                    </div>
                  )}
              </div>
            ))}
            <div ref={messagesEndRef} />

            {!isAuth && (
              <div className="flex justify-center mt-4">
                <button
                  onClick={() => (window.location.href = "/authentication")}
                  className="px-4 py-2 bg-blue-800 hover:bg-blue-700 rounded-lg text-white cursor-pointer"
                >
                  Sign In to Chat
                </button>
              </div>
            )}

            {/* Keep global quick replies for backward compatibility */}
            {isAuth && quickReplies.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-4 border-t border-blue-400 pt-3">
                <span className="text-xs text-blue-800 font-medium w-full mb-1">
                  Suggestions:
                </span>
                {quickReplies.map((reply, index) => (
                  <button
                    key={index}
                    onClick={() => handleQuickReply(reply)}
                    className="px-3 py-1 rounded-full bg-white hover:bg-blue-50 text-blue-800 text-sm border border-blue-400 shadow-sm transition-colors"
                  >
                    {reply.label}
                  </button>
                ))}
              </div>
            )}
          </div>

          <div className="p-4 border-t mt-auto bg-blue-200">
            <div className="flex gap-2">
              <input
                type="text"
                placeholder={
                  isAuth ? "Type your question here..." : "Sign in to chat"
                }
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
                disabled={!isAuth || isLoading}
                className="flex-1 px-3 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500"
                autoFocus={isAuth}
              />
              <button
                onClick={handleSendMessage}
                disabled={!isAuth || isLoading || !inputMessage.trim()}
                className={`w-10 h-10 rounded-lg ${
                  isAuth ? "bg-blue-800 hover:bg-blue-700" : "bg-blue-300"
                } text-white flex items-center justify-center`}
              >
                {isLoading ? (
                  <i className="fas fa-spinner fa-spin"></i>
                ) : (
                  <i className="fas fa-paper-plane"></i>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
