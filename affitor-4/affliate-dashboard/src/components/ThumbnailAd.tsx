import React from "react";
import { IAd } from "@/interfaces";

interface ThumbnailAdProps {
  ad: IAd;
  size?: {
    width: string;
    height: string;
  };
  className?: string;
}

const ThumbnailAd: React.FC<ThumbnailAdProps> = ({
  ad,
  size = { width: "100%", height: "110px" },
  className = "",
}) => {
  // Get thumbnail URL from video_info.cover or thumbnail property
  const thumbnailUrl =
    ad?.video_info?.cover || ad?.thumbnail || "/default-image.jpg";

  // For TikTok or YouTube, add a platform logo overlay
  const renderPlatformOverlay = () => {
    switch (ad.platform.toLowerCase()) {
      case "youtube":
        return (
          <div className="absolute bottom-1 right-1 bg-white rounded-full p-1 w-6 h-6 flex items-center justify-center shadow-sm">
            <svg
              viewBox="0 0 461.001 461.001"
              className="w-4 h-4"
              aria-hidden="true"
            >
              <g>
                <path
                  style={{ fill: "#F61C0D" }}
                  d="M365.257,67.393H95.744C42.866,67.393,0,110.259,0,163.137v134.728
                  c0,52.878,42.866,95.744,95.744,95.744h269.513c52.878,0,95.744-42.866,95.744-95.744V163.137
                  C461.001,110.259,418.135,67.393,365.257,67.393z M300.506,237.056l-126.06,60.123c-3.359,1.602-7.239-0.847-7.239-4.568V168.607
                  c0-3.774,3.982-6.22,7.348-4.514l126.06,63.881C304.363,229.873,304.298,235.248,300.506,237.056z"
                />
              </g>
            </svg>
          </div>
        );
      case "tiktok":
        return (
          <div className="absolute bottom-1 right-1 bg-white rounded-full p-1 w-6 h-6 flex items-center justify-center shadow-sm">
            <svg
              viewBox="0 0 32 32"
              className="w-4 h-4"
              aria-hidden="true"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M8.45095 19.7926C8.60723 18.4987 9.1379 17.7743 10.1379 17.0317C11.5688 16.0259 13.3561 16.5948 13.3561 16.5948V13.2197C13.7907 13.2085 14.2254 13.2343 14.6551 13.2966V17.6401C14.6551 17.6401 12.8683 17.0712 11.4375 18.0775C10.438 18.8196 9.90623 19.5446 9.7505 20.8385C9.74562 21.5411 9.87747 22.4595 10.4847 23.2536C10.3345 23.1766 10.1815 23.0889 10.0256 22.9905C8.68807 22.0923 8.44444 20.7449 8.45095 19.7926ZM22.0352 6.97898C21.0509 5.90039 20.6786 4.81139 20.5441 4.04639H21.7823C21.7823 4.04639 21.5354 6.05224 23.3347 8.02482L23.3597 8.05134C22.8747 7.7463 22.43 7.38624 22.0352 6.97898ZM28 10.0369V14.293C28 14.293 26.42 14.2312 25.2507 13.9337C23.6179 13.5176 22.5685 12.8795 22.5685 12.8795C22.5685 12.8795 21.8436 12.4245 21.785 12.3928V21.1817C21.785 21.6711 21.651 22.8932 21.2424 23.9125C20.709 25.246 19.8859 26.1212 19.7345 26.3001C19.7345 26.3001 18.7334 27.4832 16.9672 28.28C15.3752 28.9987 13.9774 28.9805 13.5596 28.9987C13.5596 28.9987 11.1434 29.0944 8.96915 27.6814C8.49898 27.3699 8.06011 27.0172 7.6582 26.6277L7.66906 26.6355C9.84383 28.0485 12.2595 27.9528 12.2595 27.9528C12.6779 27.9346 14.0756 27.9528 15.6671 27.2341C17.4317 26.4374 18.4344 25.2543 18.4344 25.2543C18.5842 25.0754 19.4111 24.2001 19.9423 22.8662C20.3498 21.8474 20.4849 20.6247 20.4849 20.1354V11.3475C20.5435 11.3797 21.2679 11.8347 21.2679 11.8347C21.2679 11.8347 22.3179 12.4734 23.9506 12.8889C25.1204 13.1864 26.7 13.2483 26.7 13.2483V9.91314C27.2404 10.0343 27.7011 10.0671 28 10.0369Z"
                fill="#EE1D52"
              />
              <path
                d="M26.7009 9.91314V13.2472C26.7009 13.2472 25.1213 13.1853 23.9515 12.8879C22.3188 12.4718 21.2688 11.8337 21.2688 11.8337C21.2688 11.8337 20.5444 11.3787 20.4858 11.3464V20.1364C20.4858 20.6258 20.3518 21.8484 19.9432 22.8672C19.4098 24.2012 18.5867 25.0764 18.4353 25.2553C18.4353 25.2553 17.4337 26.4384 15.668 27.2352C14.0765 27.9539 12.6788 27.9357 12.2604 27.9539C12.2604 27.9539 9.84473 28.0496 7.66995 26.6366L7.6591 26.6288C7.42949 26.4064 7.21336 26.1717 7.01177 25.9257C6.31777 25.0795 5.89237 24.0789 5.78547 23.7934C5.78529 23.7922 5.78529 23.791 5.78547 23.7898C5.61347 23.2937 5.25209 22.1022 5.30147 20.9482C5.38883 18.9122 6.10507 17.6625 6.29444 17.3494C6.79597 16.4957 7.44828 15.7318 8.22233 15.0919C8.90538 14.5396 9.6796 14.1002 10.5132 13.7917C11.4144 13.4295 12.3794 13.2353 13.3565 13.2197V16.5948C13.3565 16.5948 11.5691 16.028 10.1388 17.0317C9.13879 17.7743 8.60812 18.4987 8.45185 19.7926C8.44534 20.7449 8.68897 22.0923 10.0254 22.991C10.1813 23.0898 10.3343 23.1775 10.4845 23.2541C10.7179 23.5576 11.0021 23.8221 11.3255 24.0368C12.631 24.8632 13.7249 24.9209 15.1238 24.3842C16.0565 24.0254 16.7586 23.2167 17.0842 22.3206C17.2888 21.7611 17.2861 21.1978 17.2861 20.6154V4.04639H20.5417C20.6763 4.81139 21.0485 5.90039 22.0328 6.97898C22.4276 7.38624 22.8724 7.7463 23.3573 8.05134C23.5006 8.19955 24.2331 8.93231 25.1734 9.38216C25.6596 9.61469 26.1722 9.79285 26.7009 9.91314Z"
                fill="#000000"
              />
            </svg>
          </div>
        );
      default:
        return null;
    }
  };

  // Add a play button overlay for video ads
  const renderPlayButton = () => {
    return (
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="w-8 h-8 bg-black bg-opacity-60 rounded-full flex items-center justify-center">
          <div className="w-0 h-0 border-t-[5px] border-t-transparent border-l-[10px] border-l-white border-b-[5px] border-b-transparent ml-0.5"></div>
        </div>
      </div>
    );
  };

  // Handle image loading error
  const handleImageError = (
    e: React.SyntheticEvent<HTMLImageElement, Event>
  ) => {
    e.currentTarget.src = "/default-image.jpg";
  };

  return (
    <div
      className={`relative overflow-hidden rounded-md ${className}`}
      style={{ width: size.width, height: size.height }}
    >
      {/* Ad thumbnail image */}
      <img
        src={thumbnailUrl}
        alt={ad.ad_title || ad.title || "Advertisement"}
        className="w-full h-full object-cover"
        onError={handleImageError}
      />

      {/* Play button overlay */}
      {renderPlayButton()}

      {/* Platform logo overlay */}
      {renderPlatformOverlay()}

      {/* Brand name overlay (if available) */}
      {ad.brand_name && (
        <div className="absolute top-1 left-1 bg-black bg-opacity-60 rounded px-1.5 py-0.5">
          <span className="text-xs font-medium text-white truncate max-w-[120px] block">
            {ad.brand_name}
          </span>
        </div>
      )}
    </div>
  );
};

export default ThumbnailAd;
