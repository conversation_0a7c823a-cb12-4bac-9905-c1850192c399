export function CustomButton({
  children,
  className = "bg-secondary text-primary-foreground",
  id,
  onClick,
}: {
  children: React.ReactNode;
  id: string;
  className?: string;
  onClick?: () => void;
}) {
  return (
    <button
      onClick={onClick}
      id={id}
      className={`text-[15px] flex items-center justify-center gap-2 px-[16px] py-[8px] text-sm text-primary-foreground rounded-lg 
				   border-solid border-[1px] cursor-pointer whitespace-nowrap
				  hover:transform hover:-translate-y-0.5 transaction-all duration-200 ${className}`}
    >
      {children}
    </button>
  );
}



