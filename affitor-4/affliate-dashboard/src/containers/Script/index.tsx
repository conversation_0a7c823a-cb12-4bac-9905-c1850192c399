import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Con<PERSON>er,
  TextField,
  Typography,
  Paper,
  CircularProgress,
  IconButton,
  Snackbar,
  Alert,
  <PERSON><PERSON>r,
  <PERSON>ltip,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  Switch,
  FormControlLabel,
} from "@mui/material";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import YouTubeIcon from "@mui/icons-material/YouTube";
import ClearIcon from "@mui/icons-material/Clear";

// Function to extract video ID from YouTube URL
const extractYouTubeVideoId = (url: string): string | null => {
  const regExp =
    /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=|shorts\/)([^#&?]*).*/;
  const match = url.match(regExp);

  return match && match[2].length === 11 ? match[2] : null;
};

const ScriptContainer: React.FC = () => {
  const [youtubeUrl, setYoutubeUrl] = useState("");
  const [transcript, setTranscript] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [copySuccess, setCopySuccess] = useState(false);

  // Only keep the timestamps toggle, remove dark mode
  const [showTimestamps, setShowTimestamps] = useState(true);
  // Remove dark transcript state

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setYoutubeUrl(e.target.value);
  };

  const handleGetTranscript = async () => {
    if (!youtubeUrl.trim()) {
      setError("Please enter a YouTube URL");
      return;
    }

    setLoading(true);
    setError(null);
    setTranscript("");

    try {
      // Extract video ID from the URL
      const videoId = extractYouTubeVideoId(youtubeUrl);

      if (!videoId) {
        setError("Invalid YouTube URL. Could not extract video ID.");
        setLoading(false);
        return;
      }

      // Call our API endpoint directly
      const response = await fetch(
        `/api/social-listenings/custom-transcript/${videoId}`
      );
      const data = await response.json();

      if (!data.success || data.error) {
        setError(data.error || "No transcript found for this video.");
      } else if (!data.transcript) {
        setError("No transcript found for this video.");
      } else {
        setTranscript(data.transcript);
      }
    } catch (err) {
      setError("An unexpected error occurred. Please try again.");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleCopyToClipboard = () => {
    if (transcript) {
      navigator.clipboard
        .writeText(transcript)
        .then(() => {
          setCopySuccess(true);
        })
        .catch(() => {
          setError("Failed to copy to clipboard");
        });
    }
  };

  const handleCloseSnackbar = () => {
    setCopySuccess(false);
  };

  const handleClearInput = () => {
    setYoutubeUrl("");
  };

  const handleClearTranscript = () => {
    setTranscript("");
  };

  // Updated function to parse the new transcript format with single timestamps
  const formatTranscriptWithTimestamps = (text: string) => {
    // Pattern to match "00:00 : text" format
    const timestampRegex =
      /(\d{1,2}:\d{2})\s*:\s*([\s\S]*?)(?=\n\d{1,2}:\d{2}\s*:|\n$|$)/g;
    const matches = Array.from(text.matchAll(timestampRegex));

    if (matches && matches.length > 0) {
      return matches.map((match, index) => {
        const timestamp = match[1];
        const content = match[2].trim();
        const isMusic =
          content.includes("[âm nhạc]") || content.includes("[music]");

        return {
          timestamp,
          content,
          id: index,
          isMusic,
        };
      });
    } else {
      // Fallback if the regex doesn't match
      return text
        .split("\n")
        .filter((line) => line.trim())
        .map((line, index) => ({
          timestamp: "",
          content: line,
          id: index,
          isMusic: false,
        }));
    }
  };

  return (
    <Container maxWidth="md" sx={{ py: { xs: 3, md: 5 } }}>
      <Card elevation={3} sx={{ overflow: "hidden", borderRadius: 2 }}>
        <Box
          sx={{
            bgcolor: "primary.main",
            color: "primary.contrastText",
            py: 2,
            px: 3,
            display: "flex",
            alignItems: "center",
            gap: 1,
          }}
        >
          <YouTubeIcon fontSize="large" />
          <Typography variant="h5" component="h1">
            YouTube Transcript Generator
          </Typography>
        </Box>

        <CardContent sx={{ p: { xs: 2, md: 3 } }}>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            Enter a YouTube video URL to extract its transcript. This tool works
            with videos that have captions available.
          </Typography>

          <Box
            sx={{
              display: "flex",
              flexDirection: { xs: "column", sm: "row" },
              gap: 2,
              mb: 3,
            }}
          >
            <TextField
              fullWidth
              label="YouTube Video URL"
              variant="outlined"
              value={youtubeUrl}
              onChange={handleUrlChange}
              placeholder="https://www.youtube.com/watch?v=..."
              disabled={loading}
              InputProps={{
                endAdornment: youtubeUrl && (
                  <Tooltip title="Clear">
                    <IconButton
                      size="small"
                      onClick={handleClearInput}
                      edge="end"
                    >
                      <ClearIcon />
                    </IconButton>
                  </Tooltip>
                ),
              }}
            />
            <Button
              variant="contained"
              color="primary"
              onClick={handleGetTranscript}
              disabled={loading || !youtubeUrl.trim()}
              sx={{
                minWidth: { sm: 150 },
                height: { sm: 56 },
                px: 3,
              }}
              startIcon={
                loading ? <CircularProgress size={20} color="inherit" /> : null
              }
            >
              {loading ? "Processing..." : "Get Transcript"}
            </Button>
          </Box>

          {error && (
            <Alert
              severity="error"
              sx={{
                mb: 3,
                "& .MuiAlert-message": {
                  width: "100%",
                },
              }}
            >
              {error}
            </Alert>
          )}

          {transcript && (
            <>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  mb: 2,
                  flexWrap: "wrap",
                  gap: 1,
                }}
              >
                <Typography variant="h6" color="text.primary">
                  Transcript Results
                </Typography>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={showTimestamps}
                        onChange={(e) => setShowTimestamps(e.target.checked)}
                        size="small"
                      />
                    }
                    label={
                      <Typography variant="body2">
                        {showTimestamps ? "Show Timestamps" : "Hide Timestamps"}
                      </Typography>
                    }
                  />
                  <Tooltip title="Copy to clipboard">
                    <IconButton
                      onClick={handleCopyToClipboard}
                      color="primary"
                      aria-label="copy to clipboard"
                    >
                      <ContentCopyIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Clear transcript">
                    <IconButton
                      onClick={handleClearTranscript}
                      color="default"
                      aria-label="clear transcript"
                    >
                      <ClearIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
              </Box>

              <Paper
                variant="outlined"
                sx={{
                  maxHeight: isMobile ? 350 : 450,
                  overflow: "auto",
                  bgcolor: "background.paper",
                  borderRadius: 2,
                  boxShadow: 2,
                  "&::-webkit-scrollbar": {
                    width: "8px",
                  },
                  "&::-webkit-scrollbar-thumb": {
                    backgroundColor: "rgba(0,0,0,0.2)",
                    borderRadius: "4px",
                  },
                  "&::-webkit-scrollbar-track": {
                    backgroundColor: "rgba(0,0,0,0.05)",
                  },
                }}
              >
                <Box sx={{ p: 0 }}>
                  {formatTranscriptWithTimestamps(transcript).map((item) => (
                    <Box
                      key={item.id}
                      sx={{
                        display: "flex",
                        borderBottom: "1px solid",
                        borderColor: "divider",
                        "&:hover": {
                          bgcolor: "action.hover",
                        },
                      }}
                    >
                      {showTimestamps && item.timestamp && (
                        <Box
                          sx={{
                            p: 2,
                            width: "80px",
                            minWidth: "80px",
                            color: "primary.main",
                            fontWeight: "medium",
                            fontFamily: '"Roboto Mono", monospace',
                            fontSize: "0.85rem",
                            borderRight: "1px solid",
                            borderColor: "divider",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "flex-start",
                          }}
                        >
                          {item.timestamp}
                        </Box>
                      )}
                      <Box
                        sx={{
                          p: 2,
                          flex: 1,
                          fontSize: "0.95rem",
                          lineHeight: 1.5,
                          fontWeight: item.isMusic ? "normal" : "medium",
                          fontStyle: item.isMusic ? "italic" : "normal",
                          color: item.isMusic
                            ? "text.secondary"
                            : "text.primary",
                        }}
                      >
                        {item.content}
                      </Box>
                    </Box>
                  ))}
                </Box>
              </Paper>
            </>
          )}
        </CardContent>
      </Card>

      <Box sx={{ mt: 4, textAlign: "center" }}>
        <Typography variant="caption" color="text.secondary">
          Note: This tool can only extract transcripts from videos that have
          captions enabled.
        </Typography>
      </Box>

      <Snackbar
        open={copySuccess}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity="success"
          variant="filled"
          sx={{ width: "100%" }}
        >
          Transcript copied to clipboard!
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default ScriptContainer;
