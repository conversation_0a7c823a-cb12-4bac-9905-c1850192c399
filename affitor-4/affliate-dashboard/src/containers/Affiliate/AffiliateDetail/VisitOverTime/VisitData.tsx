interface IVisitOverTimeProps {
  totalVisits: number;
  avgVisitDuration: number;
  bounceRate: number;
  pagePerVisit: number;
}

export default function VisitData({
  totalVisits = 1100000,
  avgVisitDuration = 9.2 * 60,
  bounceRate = 70,
  pagePerVisit = 2.5,
}: IVisitOverTimeProps) {
  return (
    <div className="flex justify-between items-start text-center">
      <MetricColumn
        title="Monthly Visits"
        value={
          totalVisits >= 1000000
            ? (totalVisits / 1000000).toFixed(1) + "M"
            : totalVisits >= 1000
            ? (totalVisits / 1000).toFixed(1) + "K"
            : totalVisits.toString()
        }
      />
      <MetricColumn
        title="Avg. Visit Duration"
        value={new Date(avgVisitDuration * 1000).toISOString().substr(11, 8)}
      />
      <MetricColumn title="Pages/Visit" value={pagePerVisit.toString()} />
      <MetricColumn title="Bounce Rate" value={`${bounceRate.toFixed(2)}%`} />
    </div>
  );
}

function MetricColumn({ title, value }: { title: string; value: string }) {
  return (
    <div className="flex-1">
      <h3 className="text-sm text-slate-600 dark:text-slate-400 mb-1">{title}</h3>
      <p className="font-bold text-xl text-slate-800 dark:text-white">{value}</p>
    </div>
  );
}
